// Services API pour la plateforme centralisée de gestion des médicaments
import { generateId, generateNumber } from '../models';

// Configuration de base
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001/api';
const API_TIMEOUT = 10000;

// Simulation de données pour le développement
const MOCK_DATA = {
  medicines: [
    {
      id: '1',
      name: 'Paracetamol',
      dosage: '500mg',
      form: 'tablet',
      manufacturer: 'PharmaCorp',
      batchNumber: '*********',
      expiryDate: '2025-12-31',
      quantity: 450,
      minQuantity: 100,
      price: 0.25,
      status: 'active',
      createdAt: '2024-01-01T00:00:00Z'
    },
    {
      id: '2',
      name: 'Amoxicillin',
      dosage: '250mg',
      form: 'capsule',
      manufacturer: 'MediLab',
      batchNumber: '*********',
      expiryDate: '2025-08-15',
      quantity: 78,
      minQuantity: 150,
      price: 0.45,
      status: 'low_stock',
      createdAt: '2024-01-01T00:00:00Z'
    }
  ],
  
  suppliers: [
    {
      id: '1',
      name: 'PharmaCorp International',
      type: 'manufacturer',
      country: 'Germany',
      address: '123 Pharma Street, Berlin',
      phone: '+49 30 12345678',
      email: '<EMAIL>',
      website: 'www.pharmacorp.de',
      contactPerson: 'Dr. Hans Mueller',
      licenseNumber: 'DE-PHARM-2024-001',
      taxId: 'DE123456789',
      status: 'active',
      certifications: ['ISO 9001', 'GMP', 'FDA'],
      contractStartDate: '2024-01-01',
      contractEndDate: '2026-12-31',
      paymentTerms: '30 days',
      deliveryTerms: 'FOB Hamburg',
      rating: 4.8,
      totalContracts: 15,
      createdAt: '2024-01-01T00:00:00Z'
    },
    {
      id: '2',
      name: 'MediLab Solutions',
      type: 'distributor',
      country: 'France',
      address: '456 Medical Avenue, Lyon',
      phone: '+33 4 12345678',
      email: '<EMAIL>',
      website: 'www.medilab.fr',
      contactPerson: 'Marie Dubois',
      licenseNumber: 'FR-DIST-2024-002',
      taxId: 'FR987654321',
      status: 'active',
      certifications: ['ISO 13485', 'GDP'],
      contractStartDate: '2024-02-01',
      contractEndDate: '2025-12-31',
      paymentTerms: '45 days',
      deliveryTerms: 'CIF Destination',
      rating: 4.5,
      totalContracts: 8,
      createdAt: '2024-02-01T00:00:00Z'
    }
  ],
  
  amm: [
    {
      id: '1',
      ammNumber: 'AMM-2024-001',
      medicineName: 'Paracetamol 500mg',
      activeIngredient: 'Paracetamol',
      dosage: '500mg',
      form: 'tablet',
      manufacturer: 'PharmaCorp',
      indication: 'Pain relief and fever reduction',
      contraindications: 'Severe liver disease',
      sideEffects: 'Rare: skin rash, nausea',
      dosageInstructions: '1-2 tablets every 4-6 hours, max 8 tablets/day',
      storageConditions: 'Store below 25°C, dry place',
      shelfLife: '3 years',
      packagingInfo: 'Blister pack of 20 tablets',
      status: 'approved',
      applicationDate: '2024-01-15',
      approvalDate: '2024-03-15',
      expiryDate: '2029-03-15',
      renewalDate: '2028-03-15',
      documents: ['clinical_trial_data.pdf', 'quality_certificate.pdf'],
      inspector: 'Dr. Sarah Johnson',
      inspectionNotes: 'All requirements met',
      fee: 5000,
      createdAt: '2024-01-15T00:00:00Z'
    }
  ],
  
  tenders: [
    {
      id: '1',
      tenderNumber: 'TND-2024-001',
      title: 'Supply of Essential Medicines for National Hospitals',
      description: 'Procurement of essential medicines for 50 national hospitals',
      type: 'open',
      category: 'medicines',
      estimatedValue: 2500000,
      currency: 'USD',
      publishDate: '2024-01-01',
      submissionDeadline: '2024-02-15',
      openingDate: '2024-02-16',
      evaluationCriteria: 'Price (60%), Quality (30%), Delivery (10%)',
      technicalRequirements: 'WHO prequalified medicines only',
      status: 'active',
      requestingOrganization: 'Ministry of Health',
      contactPerson: 'John Smith',
      documents: ['tender_document.pdf', 'technical_specs.pdf'],
      bids: [],
      selectedBid: null,
      contractValue: 0,
      createdAt: '2024-01-01T00:00:00Z'
    }
  ],
  
  centralStock: [
    {
      id: '1',
      medicineId: '1',
      medicineName: 'Paracetamol 500mg',
      batchNumber: '*********',
      manufacturingDate: '2024-01-15',
      expiryDate: '2025-12-31',
      quantity: 10000,
      unitPrice: 0.25,
      totalValue: 2500,
      supplierId: '1',
      supplierName: 'PharmaCorp International',
      warehouseLocation: 'A-01-001',
      storageConditions: 'Room temperature',
      qrCode: 'QR-*********',
      barcode: '1234567890123',
      status: 'active',
      qualityCheck: {
        status: 'approved',
        inspector: 'Dr. Quality Control',
        date: '2024-01-16',
        notes: 'All tests passed',
        approved: true
      },
      movements: [],
      reservations: [],
      createdAt: '2024-01-16T00:00:00Z'
    }
  ]
};

// Classe principale pour les services API
class ApiService {
  constructor() {
    this.baseURL = API_BASE_URL;
    this.timeout = API_TIMEOUT;
    this.mockMode = process.env.NODE_ENV === 'development';
  }
  
  // Méthode générique pour les requêtes HTTP
  async request(endpoint, options = {}) {
    if (this.mockMode) {
      return this.mockRequest(endpoint, options);
    }
    
    const url = `${this.baseURL}${endpoint}`;
    const config = {
      timeout: this.timeout,
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    };
    
    try {
      const response = await fetch(url, config);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  }
  
  // Simulation des requêtes pour le développement
  async mockRequest(endpoint, options = {}) {
    // Simulation d'un délai réseau
    await new Promise(resolve => setTimeout(resolve, 100 + Math.random() * 500));
    
    const [, resource, id] = endpoint.split('/');
    const method = options.method || 'GET';
    
    switch (method) {
      case 'GET':
        if (id) {
          const item = MOCK_DATA[resource]?.find(item => item.id === id);
          if (!item) throw new Error('Item not found');
          return { data: item, success: true };
        }
        return { 
          data: MOCK_DATA[resource] || [], 
          success: true,
          total: MOCK_DATA[resource]?.length || 0
        };
        
      case 'POST':
        const newItem = {
          ...options.body,
          id: generateId(),
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
        
        if (!MOCK_DATA[resource]) MOCK_DATA[resource] = [];
        MOCK_DATA[resource].push(newItem);
        
        return { data: newItem, success: true };
        
      case 'PUT':
        if (!MOCK_DATA[resource]) throw new Error('Resource not found');
        
        const index = MOCK_DATA[resource].findIndex(item => item.id === id);
        if (index === -1) throw new Error('Item not found');
        
        MOCK_DATA[resource][index] = {
          ...MOCK_DATA[resource][index],
          ...options.body,
          updatedAt: new Date().toISOString()
        };
        
        return { data: MOCK_DATA[resource][index], success: true };
        
      case 'DELETE':
        if (!MOCK_DATA[resource]) throw new Error('Resource not found');
        
        const deleteIndex = MOCK_DATA[resource].findIndex(item => item.id === id);
        if (deleteIndex === -1) throw new Error('Item not found');
        
        MOCK_DATA[resource].splice(deleteIndex, 1);
        return { success: true };
        
      default:
        throw new Error(`Method ${method} not supported`);
    }
  }
  
  // Méthodes CRUD génériques
  async getAll(resource, params = {}) {
    const queryString = new URLSearchParams(params).toString();
    const endpoint = `/${resource}${queryString ? `?${queryString}` : ''}`;
    return this.request(endpoint);
  }
  
  async getById(resource, id) {
    return this.request(`/${resource}/${id}`);
  }
  
  async create(resource, data) {
    return this.request(`/${resource}`, {
      method: 'POST',
      body: data
    });
  }
  
  async update(resource, id, data) {
    return this.request(`/${resource}/${id}`, {
      method: 'PUT',
      body: data
    });
  }
  
  async delete(resource, id) {
    return this.request(`/${resource}/${id}`, {
      method: 'DELETE'
    });
  }
}

// Instance singleton
const apiService = new ApiService();

// Services spécialisés pour chaque module
export const medicineService = {
  getAll: (params) => apiService.getAll('medicines', params),
  getById: (id) => apiService.getById('medicines', id),
  create: (data) => apiService.create('medicines', data),
  update: (id, data) => apiService.update('medicines', id, data),
  delete: (id) => apiService.delete('medicines', id),
  
  // Méthodes spécialisées
  getLowStock: () => apiService.getAll('medicines', { status: 'low_stock' }),
  getExpiringSoon: (days = 30) => apiService.getAll('medicines', { expiring_in: days }),
  searchByName: (name) => apiService.getAll('medicines', { search: name })
};

export const supplierService = {
  getAll: (params) => apiService.getAll('suppliers', params),
  getById: (id) => apiService.getById('suppliers', id),
  create: (data) => apiService.create('suppliers', data),
  update: (id, data) => apiService.update('suppliers', id, data),
  delete: (id) => apiService.delete('suppliers', id),
  
  // Méthodes spécialisées
  getByType: (type) => apiService.getAll('suppliers', { type }),
  getByCountry: (country) => apiService.getAll('suppliers', { country }),
  getActive: () => apiService.getAll('suppliers', { status: 'active' })
};

export const ammService = {
  getAll: (params) => apiService.getAll('amm', params),
  getById: (id) => apiService.getById('amm', id),
  create: (data) => apiService.create('amm', data),
  update: (id, data) => apiService.update('amm', id, data),
  delete: (id) => apiService.delete('amm', id),
  
  // Méthodes spécialisées
  getPending: () => apiService.getAll('amm', { status: 'pending' }),
  getExpiringSoon: (days = 90) => apiService.getAll('amm', { expiring_in: days }),
  getByManufacturer: (manufacturer) => apiService.getAll('amm', { manufacturer })
};

export const tenderService = {
  getAll: (params) => apiService.getAll('tenders', params),
  getById: (id) => apiService.getById('tenders', id),
  create: (data) => apiService.create('tenders', data),
  update: (id, data) => apiService.update('tenders', id, data),
  delete: (id) => apiService.delete('tenders', id),
  
  // Méthodes spécialisées
  getActive: () => apiService.getAll('tenders', { status: 'active' }),
  getByCategory: (category) => apiService.getAll('tenders', { category }),
  submitBid: (tenderId, bidData) => apiService.create(`tenders/${tenderId}/bids`, bidData)
};

export const centralStockService = {
  getAll: (params) => apiService.getAll('centralStock', params),
  getById: (id) => apiService.getById('centralStock', id),
  create: (data) => apiService.create('centralStock', data),
  update: (id, data) => apiService.update('centralStock', id, data),
  delete: (id) => apiService.delete('centralStock', id),
  
  // Méthodes spécialisées
  getLowStock: () => apiService.getAll('centralStock', { low_stock: true }),
  getExpiring: (days = 30) => apiService.getAll('centralStock', { expiring_in: days }),
  getByLocation: (location) => apiService.getAll('centralStock', { location }),
  moveStock: (id, moveData) => apiService.create(`centralStock/${id}/movements`, moveData)
};

export default apiService;
