import React, { useState } from 'react';
import { 
  Plus, 
  Search, 
  ArrowRight, 
  CheckCircle, 
  Clock, 
  AlertCircle,
  Package,
  Truck,
  Building2,
  Hospital,
  User
} from 'lucide-react';

const Workflow = () => {
  const [activeTab, setActiveTab] = useState('active');
  const [showCreateForm, setShowCreateForm] = useState(false);

  const workflows = [
    {
      id: 'WF001',
      medicine: 'Paracetamol 500mg',
      quantity: 500,
      from: { type: 'supplier', name: 'MediSupply Co.' },
      to: { type: 'pharmacy', name: 'Central Pharmacy' },
      status: 'in_transit',
      createdAt: '2024-01-15T10:00:00Z',
      estimatedDelivery: '2024-01-16T14:00:00Z',
      batchNumber: 'PC2024001',
      steps: [
        { id: 1, name: 'Order Placed', status: 'completed', timestamp: '2024-01-15T10:00:00Z' },
        { id: 2, name: 'Picked & Packed', status: 'completed', timestamp: '2024-01-15T14:00:00Z' },
        { id: 3, name: 'In Transit', status: 'active', timestamp: '2024-01-15T16:00:00Z' },
        { id: 4, name: 'Delivered', status: 'pending', timestamp: null },
        { id: 5, name: 'Verified & Stored', status: 'pending', timestamp: null }
      ]
    },
    {
      id: 'WF002',
      medicine: 'Amoxicillin 250mg',
      quantity: 200,
      from: { type: 'pharmacy', name: 'Central Pharmacy' },
      to: { type: 'hospital', name: 'City General Hospital' },
      status: 'completed',
      createdAt: '2024-01-14T09:00:00Z',
      estimatedDelivery: '2024-01-14T15:00:00Z',
      batchNumber: 'ML2024002',
      steps: [
        { id: 1, name: 'Transfer Request', status: 'completed', timestamp: '2024-01-14T09:00:00Z' },
        { id: 2, name: 'Approved', status: 'completed', timestamp: '2024-01-14T09:30:00Z' },
        { id: 3, name: 'Prepared', status: 'completed', timestamp: '2024-01-14T11:00:00Z' },
        { id: 4, name: 'In Transit', status: 'completed', timestamp: '2024-01-14T12:00:00Z' },
        { id: 5, name: 'Delivered & Verified', status: 'completed', timestamp: '2024-01-14T15:00:00Z' }
      ]
    },
    {
      id: 'WF003',
      medicine: 'Ibuprofen 400mg',
      quantity: 100,
      from: { type: 'hospital', name: 'City General Hospital' },
      to: { type: 'patient', name: 'John Doe' },
      status: 'dispensed',
      createdAt: '2024-01-16T08:00:00Z',
      estimatedDelivery: '2024-01-16T08:30:00Z',
      batchNumber: 'HP2024003',
      steps: [
        { id: 1, name: 'Prescription Received', status: 'completed', timestamp: '2024-01-16T08:00:00Z' },
        { id: 2, name: 'Verified by Pharmacist', status: 'completed', timestamp: '2024-01-16T08:15:00Z' },
        { id: 3, name: 'Dispensed to Patient', status: 'completed', timestamp: '2024-01-16T08:30:00Z' }
      ]
    }
  ];

  const [newWorkflow, setNewWorkflow] = useState({
    medicine: '',
    quantity: '',
    fromType: 'supplier',
    fromName: '',
    toType: 'pharmacy',
    toName: '',
    batchNumber: ''
  });

  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-success-500" />;
      case 'in_transit':
        return <Truck className="h-5 w-5 text-primary-500" />;
      case 'dispensed':
        return <User className="h-5 w-5 text-success-500" />;
      case 'pending':
        return <Clock className="h-5 w-5 text-warning-500" />;
      default:
        return <AlertCircle className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusBadge = (status) => {
    switch (status) {
      case 'completed':
        return <span className="status-active">Completed</span>;
      case 'in_transit':
        return <span className="status-warning">In Transit</span>;
      case 'dispensed':
        return <span className="status-active">Dispensed</span>;
      case 'pending':
        return <span className="status-inactive">Pending</span>;
      default:
        return <span className="status-inactive">Unknown</span>;
    }
  };

  const getEntityIcon = (type) => {
    switch (type) {
      case 'supplier':
        return <Package className="h-4 w-4" />;
      case 'pharmacy':
        return <Building2 className="h-4 w-4" />;
      case 'hospital':
        return <Hospital className="h-4 w-4" />;
      case 'patient':
        return <User className="h-4 w-4" />;
      default:
        return <Package className="h-4 w-4" />;
    }
  };

  const filteredWorkflows = workflows.filter(workflow => {
    if (activeTab === 'active') return ['in_transit', 'pending'].includes(workflow.status);
    if (activeTab === 'completed') return ['completed', 'dispensed'].includes(workflow.status);
    return true;
  });

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Medicine Workflow</h1>
          <p className="text-gray-600">Track medicine flow from supplier to patient</p>
        </div>
        <button 
          onClick={() => setShowCreateForm(true)}
          className="btn-primary"
        >
          <Plus className="h-4 w-4 mr-2" />
          Create Workflow
        </button>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'active', name: 'Active Workflows', count: workflows.filter(w => ['in_transit', 'pending'].includes(w.status)).length },
            { id: 'completed', name: 'Completed', count: workflows.filter(w => ['completed', 'dispensed'].includes(w.status)).length },
            { id: 'all', name: 'All Workflows', count: workflows.length }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.id
                  ? 'border-primary-500 text-primary-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              {tab.name} ({tab.count})
            </button>
          ))}
        </nav>
      </div>

      {/* Create Workflow Form */}
      {showCreateForm && (
        <div className="card">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Create New Workflow</h3>
          <form className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="label">Medicine</label>
              <input
                type="text"
                className="input"
                placeholder="e.g., Paracetamol 500mg"
                value={newWorkflow.medicine}
                onChange={(e) => setNewWorkflow({...newWorkflow, medicine: e.target.value})}
              />
            </div>
            <div>
              <label className="label">Quantity</label>
              <input
                type="number"
                className="input"
                value={newWorkflow.quantity}
                onChange={(e) => setNewWorkflow({...newWorkflow, quantity: e.target.value})}
              />
            </div>
            <div>
              <label className="label">From Type</label>
              <select
                className="input"
                value={newWorkflow.fromType}
                onChange={(e) => setNewWorkflow({...newWorkflow, fromType: e.target.value})}
              >
                <option value="supplier">Supplier</option>
                <option value="pharmacy">Pharmacy</option>
                <option value="hospital">Hospital</option>
              </select>
            </div>
            <div>
              <label className="label">From Name</label>
              <input
                type="text"
                className="input"
                value={newWorkflow.fromName}
                onChange={(e) => setNewWorkflow({...newWorkflow, fromName: e.target.value})}
              />
            </div>
            <div>
              <label className="label">To Type</label>
              <select
                className="input"
                value={newWorkflow.toType}
                onChange={(e) => setNewWorkflow({...newWorkflow, toType: e.target.value})}
              >
                <option value="pharmacy">Pharmacy</option>
                <option value="hospital">Hospital</option>
                <option value="patient">Patient</option>
              </select>
            </div>
            <div>
              <label className="label">To Name</label>
              <input
                type="text"
                className="input"
                value={newWorkflow.toName}
                onChange={(e) => setNewWorkflow({...newWorkflow, toName: e.target.value})}
              />
            </div>
            <div>
              <label className="label">Batch Number</label>
              <input
                type="text"
                className="input"
                value={newWorkflow.batchNumber}
                onChange={(e) => setNewWorkflow({...newWorkflow, batchNumber: e.target.value})}
              />
            </div>
            <div className="md:col-span-2 flex gap-2">
              <button type="submit" className="btn-primary">Create Workflow</button>
              <button 
                type="button" 
                onClick={() => setShowCreateForm(false)}
                className="btn-secondary"
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Workflows List */}
      <div className="space-y-4">
        {filteredWorkflows.map((workflow) => (
          <div key={workflow.id} className="card">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-3">
                {getStatusIcon(workflow.status)}
                <div>
                  <h3 className="text-lg font-medium text-gray-900">
                    {workflow.medicine} - {workflow.quantity} units
                  </h3>
                  <p className="text-sm text-gray-500">Workflow ID: {workflow.id}</p>
                </div>
              </div>
              {getStatusBadge(workflow.status)}
            </div>

            {/* Flow Diagram */}
            <div className="flex items-center justify-between mb-6 p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-2">
                {getEntityIcon(workflow.from.type)}
                <span className="text-sm font-medium">{workflow.from.name}</span>
              </div>
              <ArrowRight className="h-5 w-5 text-gray-400" />
              <div className="flex items-center space-x-2">
                {getEntityIcon(workflow.to.type)}
                <span className="text-sm font-medium">{workflow.to.name}</span>
              </div>
            </div>

            {/* Steps Timeline */}
            <div className="space-y-3">
              <h4 className="text-sm font-medium text-gray-900">Progress Timeline</h4>
              <div className="space-y-2">
                {workflow.steps.map((step, index) => (
                  <div key={step.id} className="flex items-center space-x-3">
                    <div className={`flex-shrink-0 w-3 h-3 rounded-full ${
                      step.status === 'completed' ? 'bg-success-500' :
                      step.status === 'active' ? 'bg-primary-500' :
                      'bg-gray-300'
                    }`} />
                    <div className="flex-1">
                      <p className={`text-sm ${
                        step.status === 'completed' ? 'text-gray-900' :
                        step.status === 'active' ? 'text-primary-600 font-medium' :
                        'text-gray-500'
                      }`}>
                        {step.name}
                      </p>
                      {step.timestamp && (
                        <p className="text-xs text-gray-500">
                          {new Date(step.timestamp).toLocaleString()}
                        </p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Additional Info */}
            <div className="mt-4 pt-4 border-t border-gray-200 grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-500">Batch Number:</span>
                <span className="ml-2 font-medium">{workflow.batchNumber}</span>
              </div>
              <div>
                <span className="text-gray-500">Created:</span>
                <span className="ml-2">{new Date(workflow.createdAt).toLocaleDateString()}</span>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Workflow;
