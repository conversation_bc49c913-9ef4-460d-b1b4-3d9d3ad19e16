import React, { useState, useEffect } from 'react';
import { Save, Loader2, Plus, X } from 'lucide-react';
import { STATUSES } from '../models';

const SupplierForm = ({ supplier, onSubmit, onCancel, isLoading = false }) => {
  const [formData, setFormData] = useState({
    name: '',
    type: 'manufacturer',
    country: '',
    address: '',
    phone: '',
    email: '',
    website: '',
    contactPerson: '',
    licenseNumber: '',
    taxId: '',
    status: STATUSES.ACTIVE,
    certifications: [],
    contractStartDate: '',
    contractEndDate: '',
    paymentTerms: '',
    deliveryTerms: '',
    rating: 0,
    totalContracts: 0
  });

  const [errors, setErrors] = useState({});
  const [certificationInput, setCertificationInput] = useState('');

  const supplierTypes = [
    { value: 'manufacturer', label: 'Fabricant' },
    { value: 'distributor', label: 'Distributeur' },
    { value: 'importer', label: 'Importateur' }
  ];

  const countries = [
    'France', 'Allemagne', 'Italie', 'Espagne', 'Royaume-Uni', 'Pays-Bas',
    'Belgique', 'Suisse', 'Autriche', 'Suède', 'Danemark', 'Norvège',
    'États-Unis', 'Canada', 'Japon', 'Corée du Sud', 'Chine', 'Inde',
    'Brésil', 'Argentine', 'Mexique', 'Australie', 'Nouvelle-Zélande'
  ];

  const commonCertifications = [
    'ISO 9001', 'ISO 13485', 'GMP', 'GDP', 'FDA', 'CE', 'WHO PQ',
    'EMA', 'ANSM', 'MHRA', 'TGA', 'Health Canada', 'PMDA'
  ];

  useEffect(() => {
    if (supplier) {
      setFormData({
        name: supplier.name || '',
        type: supplier.type || 'manufacturer',
        country: supplier.country || '',
        address: supplier.address || '',
        phone: supplier.phone || '',
        email: supplier.email || '',
        website: supplier.website || '',
        contactPerson: supplier.contactPerson || '',
        licenseNumber: supplier.licenseNumber || '',
        taxId: supplier.taxId || '',
        status: supplier.status || STATUSES.ACTIVE,
        certifications: supplier.certifications || [],
        contractStartDate: supplier.contractStartDate || '',
        contractEndDate: supplier.contractEndDate || '',
        paymentTerms: supplier.paymentTerms || '',
        deliveryTerms: supplier.deliveryTerms || '',
        rating: supplier.rating || 0,
        totalContracts: supplier.totalContracts || 0
      });
    }
  }, [supplier]);

  const handleChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const addCertification = () => {
    if (certificationInput.trim() && !formData.certifications.includes(certificationInput.trim())) {
      setFormData(prev => ({
        ...prev,
        certifications: [...prev.certifications, certificationInput.trim()]
      }));
      setCertificationInput('');
    }
  };

  const removeCertification = (certification) => {
    setFormData(prev => ({
      ...prev,
      certifications: prev.certifications.filter(cert => cert !== certification)
    }));
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Le nom du fournisseur est requis';
    }

    if (!formData.country.trim()) {
      newErrors.country = 'Le pays est requis';
    }

    if (!formData.address.trim()) {
      newErrors.address = 'L\'adresse est requise';
    }

    if (!formData.phone.trim()) {
      newErrors.phone = 'Le téléphone est requis';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'L\'email est requis';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Format d\'email invalide';
    }

    if (!formData.contactPerson.trim()) {
      newErrors.contactPerson = 'La personne de contact est requise';
    }

    if (!formData.licenseNumber.trim()) {
      newErrors.licenseNumber = 'Le numéro de licence est requis';
    }

    if (formData.contractStartDate && formData.contractEndDate) {
      if (new Date(formData.contractStartDate) >= new Date(formData.contractEndDate)) {
        newErrors.contractEndDate = 'La date de fin doit être postérieure à la date de début';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (validateForm()) {
      onSubmit(formData);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Informations de base */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-gray-900">Informations de base</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Nom du fournisseur */}
          <div>
            <label className="label">Nom du fournisseur *</label>
            <input
              type="text"
              className={`input ${errors.name ? 'border-red-500 focus:ring-red-500' : ''}`}
              value={formData.name}
              onChange={(e) => handleChange('name', e.target.value)}
              placeholder="ex: PharmaCorp International"
            />
            {errors.name && <p className="text-red-500 text-xs mt-1">{errors.name}</p>}
          </div>

          {/* Type */}
          <div>
            <label className="label">Type *</label>
            <select
              className={`input ${errors.type ? 'border-red-500 focus:ring-red-500' : ''}`}
              value={formData.type}
              onChange={(e) => handleChange('type', e.target.value)}
            >
              {supplierTypes.map(type => (
                <option key={type.value} value={type.value}>{type.label}</option>
              ))}
            </select>
            {errors.type && <p className="text-red-500 text-xs mt-1">{errors.type}</p>}
          </div>

          {/* Pays */}
          <div>
            <label className="label">Pays *</label>
            <select
              className={`input ${errors.country ? 'border-red-500 focus:ring-red-500' : ''}`}
              value={formData.country}
              onChange={(e) => handleChange('country', e.target.value)}
            >
              <option value="">Sélectionner un pays</option>
              {countries.map(country => (
                <option key={country} value={country}>{country}</option>
              ))}
            </select>
            {errors.country && <p className="text-red-500 text-xs mt-1">{errors.country}</p>}
          </div>

          {/* Statut */}
          <div>
            <label className="label">Statut *</label>
            <select
              className={`input ${errors.status ? 'border-red-500 focus:ring-red-500' : ''}`}
              value={formData.status}
              onChange={(e) => handleChange('status', e.target.value)}
            >
              <option value={STATUSES.ACTIVE}>Actif</option>
              <option value={STATUSES.INACTIVE}>Inactif</option>
              <option value={STATUSES.SUSPENDED}>Suspendu</option>
              <option value={STATUSES.PENDING}>En attente</option>
            </select>
            {errors.status && <p className="text-red-500 text-xs mt-1">{errors.status}</p>}
          </div>
        </div>

        {/* Adresse */}
        <div>
          <label className="label">Adresse *</label>
          <textarea
            className={`input ${errors.address ? 'border-red-500 focus:ring-red-500' : ''}`}
            rows={3}
            value={formData.address}
            onChange={(e) => handleChange('address', e.target.value)}
            placeholder="Adresse complète du fournisseur"
          />
          {errors.address && <p className="text-red-500 text-xs mt-1">{errors.address}</p>}
        </div>
      </div>

      {/* Informations de contact */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-gray-900">Informations de contact</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Téléphone */}
          <div>
            <label className="label">Téléphone *</label>
            <input
              type="tel"
              className={`input ${errors.phone ? 'border-red-500 focus:ring-red-500' : ''}`}
              value={formData.phone}
              onChange={(e) => handleChange('phone', e.target.value)}
              placeholder="ex: +33 1 23 45 67 89"
            />
            {errors.phone && <p className="text-red-500 text-xs mt-1">{errors.phone}</p>}
          </div>

          {/* Email */}
          <div>
            <label className="label">Email *</label>
            <input
              type="email"
              className={`input ${errors.email ? 'border-red-500 focus:ring-red-500' : ''}`}
              value={formData.email}
              onChange={(e) => handleChange('email', e.target.value)}
              placeholder="ex: <EMAIL>"
            />
            {errors.email && <p className="text-red-500 text-xs mt-1">{errors.email}</p>}
          </div>

          {/* Site web */}
          <div>
            <label className="label">Site web</label>
            <input
              type="url"
              className={`input ${errors.website ? 'border-red-500 focus:ring-red-500' : ''}`}
              value={formData.website}
              onChange={(e) => handleChange('website', e.target.value)}
              placeholder="ex: https://www.pharmacorp.com"
            />
            {errors.website && <p className="text-red-500 text-xs mt-1">{errors.website}</p>}
          </div>

          {/* Personne de contact */}
          <div>
            <label className="label">Personne de contact *</label>
            <input
              type="text"
              className={`input ${errors.contactPerson ? 'border-red-500 focus:ring-red-500' : ''}`}
              value={formData.contactPerson}
              onChange={(e) => handleChange('contactPerson', e.target.value)}
              placeholder="ex: Dr. Jean Dupont"
            />
            {errors.contactPerson && <p className="text-red-500 text-xs mt-1">{errors.contactPerson}</p>}
          </div>
        </div>
      </div>

      {/* Informations légales */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-gray-900">Informations légales</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Numéro de licence */}
          <div>
            <label className="label">Numéro de licence *</label>
            <input
              type="text"
              className={`input ${errors.licenseNumber ? 'border-red-500 focus:ring-red-500' : ''}`}
              value={formData.licenseNumber}
              onChange={(e) => handleChange('licenseNumber', e.target.value)}
              placeholder="ex: FR-PHARM-2024-001"
            />
            {errors.licenseNumber && <p className="text-red-500 text-xs mt-1">{errors.licenseNumber}</p>}
          </div>

          {/* Numéro fiscal */}
          <div>
            <label className="label">Numéro fiscal</label>
            <input
              type="text"
              className={`input ${errors.taxId ? 'border-red-500 focus:ring-red-500' : ''}`}
              value={formData.taxId}
              onChange={(e) => handleChange('taxId', e.target.value)}
              placeholder="ex: FR12345678901"
            />
            {errors.taxId && <p className="text-red-500 text-xs mt-1">{errors.taxId}</p>}
          </div>
        </div>
      </div>

      {/* Certifications */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-gray-900">Certifications</h3>
        
        <div className="flex gap-2">
          <input
            type="text"
            className="input flex-1"
            value={certificationInput}
            onChange={(e) => setCertificationInput(e.target.value)}
            placeholder="Ajouter une certification"
            onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addCertification())}
          />
          <button
            type="button"
            onClick={addCertification}
            className="btn-secondary"
          >
            <Plus className="h-4 w-4" />
          </button>
        </div>

        <div className="flex flex-wrap gap-2">
          {commonCertifications.map(cert => (
            <button
              key={cert}
              type="button"
              onClick={() => {
                if (!formData.certifications.includes(cert)) {
                  setFormData(prev => ({
                    ...prev,
                    certifications: [...prev.certifications, cert]
                  }));
                }
              }}
              className="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-full hover:bg-gray-200 transition-colors"
            >
              + {cert}
            </button>
          ))}
        </div>

        {formData.certifications.length > 0 && (
          <div className="flex flex-wrap gap-2">
            {formData.certifications.map(cert => (
              <span
                key={cert}
                className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800"
              >
                {cert}
                <button
                  type="button"
                  onClick={() => removeCertification(cert)}
                  className="ml-2 text-blue-600 hover:text-blue-800"
                >
                  <X className="h-3 w-3" />
                </button>
              </span>
            ))}
          </div>
        )}
      </div>

      {/* Informations contractuelles */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-gray-900">Informations contractuelles</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Date de début de contrat */}
          <div>
            <label className="label">Date de début de contrat</label>
            <input
              type="date"
              className={`input ${errors.contractStartDate ? 'border-red-500 focus:ring-red-500' : ''}`}
              value={formData.contractStartDate}
              onChange={(e) => handleChange('contractStartDate', e.target.value)}
            />
            {errors.contractStartDate && <p className="text-red-500 text-xs mt-1">{errors.contractStartDate}</p>}
          </div>

          {/* Date de fin de contrat */}
          <div>
            <label className="label">Date de fin de contrat</label>
            <input
              type="date"
              className={`input ${errors.contractEndDate ? 'border-red-500 focus:ring-red-500' : ''}`}
              value={formData.contractEndDate}
              onChange={(e) => handleChange('contractEndDate', e.target.value)}
            />
            {errors.contractEndDate && <p className="text-red-500 text-xs mt-1">{errors.contractEndDate}</p>}
          </div>

          {/* Conditions de paiement */}
          <div>
            <label className="label">Conditions de paiement</label>
            <input
              type="text"
              className={`input ${errors.paymentTerms ? 'border-red-500 focus:ring-red-500' : ''}`}
              value={formData.paymentTerms}
              onChange={(e) => handleChange('paymentTerms', e.target.value)}
              placeholder="ex: 30 jours net"
            />
            {errors.paymentTerms && <p className="text-red-500 text-xs mt-1">{errors.paymentTerms}</p>}
          </div>

          {/* Conditions de livraison */}
          <div>
            <label className="label">Conditions de livraison</label>
            <input
              type="text"
              className={`input ${errors.deliveryTerms ? 'border-red-500 focus:ring-red-500' : ''}`}
              value={formData.deliveryTerms}
              onChange={(e) => handleChange('deliveryTerms', e.target.value)}
              placeholder="ex: FOB Hamburg"
            />
            {errors.deliveryTerms && <p className="text-red-500 text-xs mt-1">{errors.deliveryTerms}</p>}
          </div>
        </div>
      </div>

      {/* Actions */}
      <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
        <button
          type="button"
          onClick={onCancel}
          className="btn-secondary"
          disabled={isLoading}
        >
          Annuler
        </button>
        <button
          type="submit"
          className="btn-primary"
          disabled={isLoading}
        >
          {isLoading ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Enregistrement...
            </>
          ) : (
            <>
              <Save className="h-4 w-4 mr-2" />
              {supplier ? 'Modifier' : 'Ajouter'}
            </>
          )}
        </button>
      </div>
    </form>
  );
};

export default SupplierForm;
