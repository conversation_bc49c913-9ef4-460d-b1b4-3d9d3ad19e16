// Configuration Firebase pour la plateforme de gestion pharmaceutique
import { initializeApp } from "firebase/app";
import { getFirestore, connectFirestoreEmulator } from "firebase/firestore";
import { getAuth, connectAuthEmulator } from "firebase/auth";
import { getStorage, connectStorageEmulator } from "firebase/storage";
import { getFunctions, connectFunctionsEmulator } from "firebase/functions";

// Configuration Firebase
const firebaseConfig = {
  apiKey: "AIzaSyCHr0EZO9xa24ra8l988p1exNByTs6U6GI",
  authDomain: "pharmacy-management-5c5b5.firebaseapp.com",
  projectId: "pharmacy-management-5c5b5",
  storageBucket: "pharmacy-management-5c5b5.firebasestorage.app",
  messagingSenderId: "366296003647",
  appId: "1:366296003647:web:110e5dd1261da8aa13b48a"
};

// Initialiser Firebase
const app = initializeApp(firebaseConfig);

// Initialiser les services Firebase
export const db = getFirestore(app);
export const auth = getAuth(app);
export const storage = getStorage(app);
export const functions = getFunctions(app);

// Configuration pour le développement local (émulateurs)
if (process.env.NODE_ENV === 'development' && !window.location.hostname.includes('localhost')) {
  // Connecter aux émulateurs Firebase si en développement
  try {
    connectFirestoreEmulator(db, 'localhost', 8080);
    connectAuthEmulator(auth, 'http://localhost:9099');
    connectStorageEmulator(storage, 'localhost', 9199);
    connectFunctionsEmulator(functions, 'localhost', 5001);
  } catch (error) {
    console.log('Émulateurs Firebase déjà connectés ou non disponibles');
  }
}

// Collections Firestore
export const COLLECTIONS = {
  // Collections principales
  MEDICINES: 'medicines',
  PHARMACIES: 'pharmacies',
  HOSPITALS: 'hospitals',
  SUPPLIERS: 'suppliers',
  
  // Nouveaux modules
  AMM: 'amm',
  TENDERS: 'tenders',
  CENTRAL_STOCK: 'centralStock',
  DISTRIBUTIONS: 'distributions',
  PHARMACOVIGILANCE: 'pharmacovigilance',
  PRICE_REGULATIONS: 'priceRegulations',
  PUBLIC_REPORTS: 'publicReports',
  
  // Collections système
  USERS: 'users',
  AUDIT_LOGS: 'auditLogs',
  NOTIFICATIONS: 'notifications',
  SETTINGS: 'settings',
  
  // Collections de référence
  COUNTRIES: 'countries',
  CURRENCIES: 'currencies',
  MEDICINE_CATEGORIES: 'medicineCategories',
  CERTIFICATIONS: 'certifications'
};

// Règles de sécurité par défaut
export const SECURITY_RULES = {
  // Rôles utilisateur
  ROLES: {
    ADMIN: 'admin',
    MANAGER: 'manager',
    PHARMACIST: 'pharmacist',
    INSPECTOR: 'inspector',
    VIEWER: 'viewer'
  },
  
  // Permissions par module
  PERMISSIONS: {
    MEDICINES: ['read', 'write', 'delete'],
    PHARMACIES: ['read', 'write', 'delete'],
    HOSPITALS: ['read', 'write', 'delete'],
    SUPPLIERS: ['read', 'write', 'delete'],
    AMM: ['read', 'write', 'approve', 'reject'],
    TENDERS: ['read', 'write', 'evaluate', 'award'],
    CENTRAL_STOCK: ['read', 'write', 'move', 'audit'],
    DISTRIBUTIONS: ['read', 'write', 'approve', 'track'],
    PHARMACOVIGILANCE: ['read', 'write', 'investigate', 'close'],
    PRICE_REGULATIONS: ['read', 'write', 'approve'],
    PUBLIC_REPORTS: ['read', 'write', 'respond'],
    AUDIT_LOGS: ['read'],
    SETTINGS: ['read', 'write']
  }
};

// Configuration des index Firestore
export const FIRESTORE_INDEXES = [
  // Index pour les médicaments
  {
    collection: COLLECTIONS.MEDICINES,
    fields: [
      { field: 'status', order: 'asc' },
      { field: 'expiryDate', order: 'asc' }
    ]
  },
  {
    collection: COLLECTIONS.MEDICINES,
    fields: [
      { field: 'quantity', order: 'asc' },
      { field: 'minQuantity', order: 'asc' }
    ]
  },
  
  // Index pour les fournisseurs
  {
    collection: COLLECTIONS.SUPPLIERS,
    fields: [
      { field: 'type', order: 'asc' },
      { field: 'country', order: 'asc' },
      { field: 'status', order: 'asc' }
    ]
  },
  
  // Index pour AMM
  {
    collection: COLLECTIONS.AMM,
    fields: [
      { field: 'status', order: 'asc' },
      { field: 'expiryDate', order: 'asc' }
    ]
  },
  
  // Index pour le stock central
  {
    collection: COLLECTIONS.CENTRAL_STOCK,
    fields: [
      { field: 'medicineId', order: 'asc' },
      { field: 'expiryDate', order: 'asc' }
    ]
  },
  {
    collection: COLLECTIONS.CENTRAL_STOCK,
    fields: [
      { field: 'warehouseLocation', order: 'asc' },
      { field: 'status', order: 'asc' }
    ]
  },
  
  // Index pour les distributions
  {
    collection: COLLECTIONS.DISTRIBUTIONS,
    fields: [
      { field: 'status', order: 'asc' },
      { field: 'requestDate', order: 'desc' }
    ]
  },
  {
    collection: COLLECTIONS.DISTRIBUTIONS,
    fields: [
      { field: 'destinationType', order: 'asc' },
      { field: 'destinationId', order: 'asc' }
    ]
  },
  
  // Index pour la pharmacovigilance
  {
    collection: COLLECTIONS.PHARMACOVIGILANCE,
    fields: [
      { field: 'severity', order: 'asc' },
      { field: 'reportDate', order: 'desc' }
    ]
  },
  {
    collection: COLLECTIONS.PHARMACOVIGILANCE,
    fields: [
      { field: 'medicineId', order: 'asc' },
      { field: 'status', order: 'asc' }
    ]
  },
  
  // Index pour les logs d'audit
  {
    collection: COLLECTIONS.AUDIT_LOGS,
    fields: [
      { field: 'userId', order: 'asc' },
      { field: 'timestamp', order: 'desc' }
    ]
  },
  {
    collection: COLLECTIONS.AUDIT_LOGS,
    fields: [
      { field: 'module', order: 'asc' },
      { field: 'action', order: 'asc' },
      { field: 'timestamp', order: 'desc' }
    ]
  }
];

// Configuration du cache Firestore
export const CACHE_CONFIG = {
  // Taille du cache (en MB)
  cacheSizeBytes: 40 * 1024 * 1024, // 40 MB
  
  // Collections à mettre en cache
  CACHED_COLLECTIONS: [
    COLLECTIONS.MEDICINES,
    COLLECTIONS.PHARMACIES,
    COLLECTIONS.HOSPITALS,
    COLLECTIONS.SUPPLIERS,
    COLLECTIONS.COUNTRIES,
    COLLECTIONS.CURRENCIES,
    COLLECTIONS.MEDICINE_CATEGORIES,
    COLLECTIONS.CERTIFICATIONS
  ],
  
  // Durée de vie du cache (en secondes)
  CACHE_TTL: {
    STATIC_DATA: 24 * 60 * 60, // 24 heures pour les données statiques
    DYNAMIC_DATA: 5 * 60, // 5 minutes pour les données dynamiques
    REAL_TIME_DATA: 30 // 30 secondes pour les données temps réel
  }
};

// Configuration des notifications push
export const NOTIFICATION_CONFIG = {
  // Types de notifications
  TYPES: {
    LOW_STOCK: 'low_stock',
    EXPIRY_WARNING: 'expiry_warning',
    AMM_EXPIRY: 'amm_expiry',
    TENDER_DEADLINE: 'tender_deadline',
    DISTRIBUTION_ALERT: 'distribution_alert',
    PHARMACOVIGILANCE_ALERT: 'pharmacovigilance_alert',
    SYSTEM_ALERT: 'system_alert'
  },
  
  // Canaux de notification
  CHANNELS: {
    EMAIL: 'email',
    SMS: 'sms',
    PUSH: 'push',
    IN_APP: 'in_app'
  }
};

// Fonctions utilitaires
export const getCollectionRef = (collectionName) => {
  return db.collection(collectionName);
};

export const getDocumentRef = (collectionName, documentId) => {
  return db.collection(collectionName).doc(documentId);
};

// Export par défaut
export default app;
