import React, { useRef } from "react";
import {
  BrowserRouter as Router,
  Routes,
  Route,
  useLocation,
} from "react-router-dom";
import { AppProvider } from "./contexts/AppContext";
import { AuthProvider } from "./hooks/useAuth.jsx";
import ProtectedRoute from "./components/ProtectedRoute";
import Layout from "./components/Layout";
import Dashboard from "./pages/Dashboard";
import Medicines from "./pages/Medicines";
import Pharmacies from "./pages/Pharmacies";
import Hospitals from "./pages/Hospitals";
import Inventory from "./pages/Inventory";
import Workflow from "./pages/Workflow";
import Settings from "./pages/Settings";
import Suppliers from "./pages/Suppliers";

function AppContent() {
  const location = useLocation();
  const pageRefs = {
    medicines: useRef(),
    pharmacies: useRef(),
    hospitals: useRef(),
    inventory: useRef(),
    suppliers: useRef(),
  };

  const handleAddClick = () => {
    switch (location.pathname) {
      case "/medicines":
        pageRefs.medicines.current?.handleAddClick();
        break;
      case "/pharmacies":
        pageRefs.pharmacies.current?.handleAddClick();
        break;
      case "/hospitals":
        pageRefs.hospitals.current?.handleAddClick();
        break;
      case "/inventory":
        pageRefs.inventory.current?.handleAddClick();
        break;
      case "/suppliers":
        pageRefs.suppliers.current?.handleAddClick();
        break;
    }
  };

  return (
    <ProtectedRoute>
      <Layout onAddClick={handleAddClick}>
        <Routes>
          <Route path="/" element={<Dashboard />} />
          <Route
            path="/medicines"
            element={<Medicines ref={pageRefs.medicines} />}
          />
          <Route
            path="/pharmacies"
            element={<Pharmacies ref={pageRefs.pharmacies} />}
          />
          <Route
            path="/hospitals"
            element={<Hospitals ref={pageRefs.hospitals} />}
          />
          <Route
            path="/inventory"
            element={<Inventory ref={pageRefs.inventory} />}
          />
          <Route
            path="/suppliers"
            element={<Suppliers ref={pageRefs.suppliers} />}
          />
          <Route path="/workflow" element={<Workflow />} />
          <Route path="/settings" element={<Settings />} />
        </Routes>
      </Layout>
    </ProtectedRoute>
  );
}

function App() {
  return (
    <AuthProvider>
      <AppProvider>
        <Router>
          <AppContent />
        </Router>
      </AppProvider>
    </AuthProvider>
  );
}

export default App;
