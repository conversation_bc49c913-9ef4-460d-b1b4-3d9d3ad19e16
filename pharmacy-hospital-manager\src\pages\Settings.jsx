import React, { useState } from 'react';
import { 
  Save, 
  Bell, 
  Shield, 
  Database, 
  Mail,
  Smartphone,
  Globe,
  Users
} from 'lucide-react';

const Settings = () => {
  const [activeTab, setActiveTab] = useState('general');
  const [settings, setSettings] = useState({
    general: {
      systemName: 'PharmaCare Management System',
      timezone: 'UTC-5',
      language: 'English',
      currency: 'USD'
    },
    notifications: {
      lowStockAlerts: true,
      expiryAlerts: true,
      workflowUpdates: true,
      emailNotifications: true,
      smsNotifications: false,
      lowStockThreshold: 20,
      expiryWarningDays: 30
    },
    security: {
      sessionTimeout: 30,
      passwordExpiry: 90,
      twoFactorAuth: false,
      auditLogging: true
    },
    backup: {
      autoBackup: true,
      backupFrequency: 'daily',
      retentionPeriod: 30,
      lastBackup: '2024-01-16 02:00:00'
    }
  });

  const handleSettingChange = (category, key, value) => {
    setSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        [key]: value
      }
    }));
  };

  const handleSave = () => {
    // In a real app, this would save to backend
    alert('Settings saved successfully!');
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Settings</h1>
          <p className="text-gray-600">Configure system preferences and options</p>
        </div>
        <button onClick={handleSave} className="btn-primary">
          <Save className="h-4 w-4 mr-2" />
          Save Changes
        </button>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'general', name: 'General', icon: Globe },
            { id: 'notifications', name: 'Notifications', icon: Bell },
            { id: 'security', name: 'Security', icon: Shield },
            { id: 'backup', name: 'Backup', icon: Database }
          ].map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center ${
                  activeTab === tab.id
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon className="h-4 w-4 mr-2" />
                {tab.name}
              </button>
            );
          })}
        </nav>
      </div>

      {/* General Settings */}
      {activeTab === 'general' && (
        <div className="card">
          <h3 className="text-lg font-medium text-gray-900 mb-4">General Settings</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="label">System Name</label>
              <input
                type="text"
                className="input"
                value={settings.general.systemName}
                onChange={(e) => handleSettingChange('general', 'systemName', e.target.value)}
              />
            </div>
            <div>
              <label className="label">Timezone</label>
              <select
                className="input"
                value={settings.general.timezone}
                onChange={(e) => handleSettingChange('general', 'timezone', e.target.value)}
              >
                <option value="UTC-5">UTC-5 (Eastern)</option>
                <option value="UTC-6">UTC-6 (Central)</option>
                <option value="UTC-7">UTC-7 (Mountain)</option>
                <option value="UTC-8">UTC-8 (Pacific)</option>
              </select>
            </div>
            <div>
              <label className="label">Language</label>
              <select
                className="input"
                value={settings.general.language}
                onChange={(e) => handleSettingChange('general', 'language', e.target.value)}
              >
                <option value="English">English</option>
                <option value="Spanish">Spanish</option>
                <option value="French">French</option>
              </select>
            </div>
            <div>
              <label className="label">Currency</label>
              <select
                className="input"
                value={settings.general.currency}
                onChange={(e) => handleSettingChange('general', 'currency', e.target.value)}
              >
                <option value="USD">USD ($)</option>
                <option value="EUR">EUR (€)</option>
                <option value="GBP">GBP (£)</option>
              </select>
            </div>
          </div>
        </div>
      )}

      {/* Notification Settings */}
      {activeTab === 'notifications' && (
        <div className="space-y-6">
          <div className="card">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Alert Preferences</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-900">Low Stock Alerts</p>
                  <p className="text-sm text-gray-500">Get notified when medicine stock is low</p>
                </div>
                <input
                  type="checkbox"
                  className="h-4 w-4 text-primary-600 rounded"
                  checked={settings.notifications.lowStockAlerts}
                  onChange={(e) => handleSettingChange('notifications', 'lowStockAlerts', e.target.checked)}
                />
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-900">Expiry Alerts</p>
                  <p className="text-sm text-gray-500">Get notified about medicines nearing expiry</p>
                </div>
                <input
                  type="checkbox"
                  className="h-4 w-4 text-primary-600 rounded"
                  checked={settings.notifications.expiryAlerts}
                  onChange={(e) => handleSettingChange('notifications', 'expiryAlerts', e.target.checked)}
                />
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-900">Workflow Updates</p>
                  <p className="text-sm text-gray-500">Get notified about workflow status changes</p>
                </div>
                <input
                  type="checkbox"
                  className="h-4 w-4 text-primary-600 rounded"
                  checked={settings.notifications.workflowUpdates}
                  onChange={(e) => handleSettingChange('notifications', 'workflowUpdates', e.target.checked)}
                />
              </div>
            </div>
          </div>

          <div className="card">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Notification Channels</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <Mail className="h-5 w-5 text-gray-400 mr-3" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">Email Notifications</p>
                    <p className="text-sm text-gray-500">Receive alerts via email</p>
                  </div>
                </div>
                <input
                  type="checkbox"
                  className="h-4 w-4 text-primary-600 rounded"
                  checked={settings.notifications.emailNotifications}
                  onChange={(e) => handleSettingChange('notifications', 'emailNotifications', e.target.checked)}
                />
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <Smartphone className="h-5 w-5 text-gray-400 mr-3" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">SMS Notifications</p>
                    <p className="text-sm text-gray-500">Receive alerts via SMS</p>
                  </div>
                </div>
                <input
                  type="checkbox"
                  className="h-4 w-4 text-primary-600 rounded"
                  checked={settings.notifications.smsNotifications}
                  onChange={(e) => handleSettingChange('notifications', 'smsNotifications', e.target.checked)}
                />
              </div>
            </div>
          </div>

          <div className="card">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Alert Thresholds</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="label">Low Stock Threshold (%)</label>
                <input
                  type="number"
                  className="input"
                  value={settings.notifications.lowStockThreshold}
                  onChange={(e) => handleSettingChange('notifications', 'lowStockThreshold', parseInt(e.target.value))}
                />
              </div>
              <div>
                <label className="label">Expiry Warning (days)</label>
                <input
                  type="number"
                  className="input"
                  value={settings.notifications.expiryWarningDays}
                  onChange={(e) => handleSettingChange('notifications', 'expiryWarningDays', parseInt(e.target.value))}
                />
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Security Settings */}
      {activeTab === 'security' && (
        <div className="card">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Security Settings</h3>
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="label">Session Timeout (minutes)</label>
                <input
                  type="number"
                  className="input"
                  value={settings.security.sessionTimeout}
                  onChange={(e) => handleSettingChange('security', 'sessionTimeout', parseInt(e.target.value))}
                />
              </div>
              <div>
                <label className="label">Password Expiry (days)</label>
                <input
                  type="number"
                  className="input"
                  value={settings.security.passwordExpiry}
                  onChange={(e) => handleSettingChange('security', 'passwordExpiry', parseInt(e.target.value))}
                />
              </div>
            </div>
            
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-900">Two-Factor Authentication</p>
                  <p className="text-sm text-gray-500">Require 2FA for all user logins</p>
                </div>
                <input
                  type="checkbox"
                  className="h-4 w-4 text-primary-600 rounded"
                  checked={settings.security.twoFactorAuth}
                  onChange={(e) => handleSettingChange('security', 'twoFactorAuth', e.target.checked)}
                />
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-900">Audit Logging</p>
                  <p className="text-sm text-gray-500">Log all user actions for security auditing</p>
                </div>
                <input
                  type="checkbox"
                  className="h-4 w-4 text-primary-600 rounded"
                  checked={settings.security.auditLogging}
                  onChange={(e) => handleSettingChange('security', 'auditLogging', e.target.checked)}
                />
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Backup Settings */}
      {activeTab === 'backup' && (
        <div className="card">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Backup Settings</h3>
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-900">Automatic Backup</p>
                <p className="text-sm text-gray-500">Enable automatic system backups</p>
              </div>
              <input
                type="checkbox"
                className="h-4 w-4 text-primary-600 rounded"
                checked={settings.backup.autoBackup}
                onChange={(e) => handleSettingChange('backup', 'autoBackup', e.target.checked)}
              />
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="label">Backup Frequency</label>
                <select
                  className="input"
                  value={settings.backup.backupFrequency}
                  onChange={(e) => handleSettingChange('backup', 'backupFrequency', e.target.value)}
                >
                  <option value="hourly">Hourly</option>
                  <option value="daily">Daily</option>
                  <option value="weekly">Weekly</option>
                  <option value="monthly">Monthly</option>
                </select>
              </div>
              <div>
                <label className="label">Retention Period (days)</label>
                <input
                  type="number"
                  className="input"
                  value={settings.backup.retentionPeriod}
                  onChange={(e) => handleSettingChange('backup', 'retentionPeriod', parseInt(e.target.value))}
                />
              </div>
            </div>
            
            <div className="bg-gray-50 p-4 rounded-lg">
              <p className="text-sm font-medium text-gray-900">Last Backup</p>
              <p className="text-sm text-gray-600">{settings.backup.lastBackup}</p>
              <button className="btn-secondary mt-2">
                <Database className="h-4 w-4 mr-2" />
                Run Backup Now
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Settings;
