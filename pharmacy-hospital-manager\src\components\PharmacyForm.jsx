import React, { useState, useEffect } from 'react';
import { Save, Loader2 } from 'lucide-react';

const PharmacyForm = ({ pharmacy, onSubmit, onCancel, isLoading = false }) => {
  const [formData, setFormData] = useState({
    name: '',
    address: '',
    phone: '',
    email: '',
    manager: '',
    licenseNumber: '',
    operatingHours: '',
    description: ''
  });

  const [errors, setErrors] = useState({});

  useEffect(() => {
    if (pharmacy) {
      setFormData({
        name: pharmacy.name || '',
        address: pharmacy.address || '',
        phone: pharmacy.phone || '',
        email: pharmacy.email || '',
        manager: pharmacy.manager || '',
        licenseNumber: pharmacy.licenseNumber || '',
        operatingHours: pharmacy.operatingHours || '',
        description: pharmacy.description || ''
      });
    }
  }, [pharmacy]);

  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) newErrors.name = 'Pharmacy name is required';
    if (!formData.address.trim()) newErrors.address = 'Address is required';
    if (!formData.phone.trim()) newErrors.phone = 'Phone number is required';
    if (!formData.email.trim()) newErrors.email = 'Email is required';
    if (!formData.manager.trim()) newErrors.manager = 'Manager name is required';
    if (!formData.licenseNumber.trim()) newErrors.licenseNumber = 'License number is required';
    if (!formData.operatingHours.trim()) newErrors.operatingHours = 'Operating hours are required';

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (formData.email && !emailRegex.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    // Phone validation (basic)
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    if (formData.phone && !phoneRegex.test(formData.phone.replace(/[\s\-\(\)]/g, ''))) {
      newErrors.phone = 'Please enter a valid phone number';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (validateForm()) {
      onSubmit(formData);
    }
  };

  const handleChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Pharmacy Name */}
        <div>
          <label className="label">Pharmacy Name *</label>
          <input
            type="text"
            className={`input ${errors.name ? 'border-red-500 focus:ring-red-500' : ''}`}
            value={formData.name}
            onChange={(e) => handleChange('name', e.target.value)}
            placeholder="e.g., Central Pharmacy"
          />
          {errors.name && <p className="text-red-500 text-xs mt-1">{errors.name}</p>}
        </div>

        {/* Manager */}
        <div>
          <label className="label">Manager *</label>
          <input
            type="text"
            className={`input ${errors.manager ? 'border-red-500 focus:ring-red-500' : ''}`}
            value={formData.manager}
            onChange={(e) => handleChange('manager', e.target.value)}
            placeholder="e.g., Dr. Sarah Johnson"
          />
          {errors.manager && <p className="text-red-500 text-xs mt-1">{errors.manager}</p>}
        </div>

        {/* Address */}
        <div className="md:col-span-2">
          <label className="label">Address *</label>
          <input
            type="text"
            className={`input ${errors.address ? 'border-red-500 focus:ring-red-500' : ''}`}
            value={formData.address}
            onChange={(e) => handleChange('address', e.target.value)}
            placeholder="e.g., 123 Main Street, Downtown"
          />
          {errors.address && <p className="text-red-500 text-xs mt-1">{errors.address}</p>}
        </div>

        {/* Phone */}
        <div>
          <label className="label">Phone *</label>
          <input
            type="tel"
            className={`input ${errors.phone ? 'border-red-500 focus:ring-red-500' : ''}`}
            value={formData.phone}
            onChange={(e) => handleChange('phone', e.target.value)}
            placeholder="e.g., +****************"
          />
          {errors.phone && <p className="text-red-500 text-xs mt-1">{errors.phone}</p>}
        </div>

        {/* Email */}
        <div>
          <label className="label">Email *</label>
          <input
            type="email"
            className={`input ${errors.email ? 'border-red-500 focus:ring-red-500' : ''}`}
            value={formData.email}
            onChange={(e) => handleChange('email', e.target.value)}
            placeholder="e.g., <EMAIL>"
          />
          {errors.email && <p className="text-red-500 text-xs mt-1">{errors.email}</p>}
        </div>

        {/* License Number */}
        <div>
          <label className="label">License Number *</label>
          <input
            type="text"
            className={`input ${errors.licenseNumber ? 'border-red-500 focus:ring-red-500' : ''}`}
            value={formData.licenseNumber}
            onChange={(e) => handleChange('licenseNumber', e.target.value)}
            placeholder="e.g., *********"
          />
          {errors.licenseNumber && <p className="text-red-500 text-xs mt-1">{errors.licenseNumber}</p>}
        </div>

        {/* Operating Hours */}
        <div>
          <label className="label">Operating Hours *</label>
          <input
            type="text"
            className={`input ${errors.operatingHours ? 'border-red-500 focus:ring-red-500' : ''}`}
            value={formData.operatingHours}
            onChange={(e) => handleChange('operatingHours', e.target.value)}
            placeholder="e.g., 9:00 AM - 9:00 PM"
          />
          {errors.operatingHours && <p className="text-red-500 text-xs mt-1">{errors.operatingHours}</p>}
        </div>
      </div>

      {/* Description */}
      <div>
        <label className="label">Description</label>
        <textarea
          className="input min-h-[100px] resize-none"
          value={formData.description}
          onChange={(e) => handleChange('description', e.target.value)}
          placeholder="Additional information about the pharmacy..."
          rows={4}
        />
      </div>

      {/* Actions */}
      <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
        <button
          type="button"
          onClick={onCancel}
          className="btn-secondary"
          disabled={isLoading}
        >
          Cancel
        </button>
        <button
          type="submit"
          className="btn-primary"
          disabled={isLoading}
        >
          {isLoading ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Saving...
            </>
          ) : (
            <>
              <Save className="h-4 w-4 mr-2" />
              {pharmacy ? 'Update Pharmacy' : 'Add Pharmacy'}
            </>
          )}
        </button>
      </div>
    </form>
  );
};

export default PharmacyForm;
