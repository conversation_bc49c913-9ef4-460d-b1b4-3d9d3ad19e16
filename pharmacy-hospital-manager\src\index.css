@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    @apply bg-gradient-to-br from-slate-50 to-blue-50 text-gray-900;
    font-feature-settings: "rlig" 1, "calt" 1;
    margin: 0;
    font-family: 'Inter', system-ui, Avenir, Helvetica, Arial, sans-serif;
  }

  * {
    scroll-behavior: smooth;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center rounded-lg text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none px-4 py-2.5 shadow-sm hover:shadow-md transform hover:-translate-y-0.5;
  }

  .btn-primary {
    @apply btn bg-gradient-to-r from-blue-600 to-blue-700 text-white hover:from-blue-700 hover:to-blue-800 shadow-blue-200;
  }

  .btn-secondary {
    @apply btn bg-white border border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400;
  }

  .btn-success {
    @apply btn bg-gradient-to-r from-green-600 to-green-700 text-white hover:from-green-700 hover:to-green-800 shadow-green-200;
  }

  .btn-warning {
    @apply btn bg-gradient-to-r from-amber-500 to-amber-600 text-white hover:from-amber-600 hover:to-amber-700 shadow-amber-200;
  }

  .btn-danger {
    @apply btn bg-gradient-to-r from-red-600 to-red-700 text-white hover:from-red-700 hover:to-red-800 shadow-red-200;
  }

  .card {
    @apply bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-6 transition-all duration-300 hover:shadow-xl hover:-translate-y-1;
  }

  .card-interactive {
    @apply card cursor-pointer hover:bg-white/90;
  }

  .input {
    @apply flex h-11 w-full rounded-lg border border-gray-300 bg-white/50 backdrop-blur-sm px-4 py-2 text-sm placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 focus-visible:border-blue-500 disabled:cursor-not-allowed disabled:opacity-50 transition-all duration-200;
  }

  .label {
    @apply text-sm font-semibold text-gray-700 leading-none mb-2 block;
  }

  .status-badge {
    @apply inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold shadow-sm;
  }

  .status-active {
    @apply status-badge bg-gradient-to-r from-green-100 to-green-200 text-green-800 border border-green-300;
  }

  .status-inactive {
    @apply status-badge bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700 border border-gray-300;
  }

  .status-warning {
    @apply status-badge bg-gradient-to-r from-amber-100 to-amber-200 text-amber-800 border border-amber-300;
  }

  .status-danger {
    @apply status-badge bg-gradient-to-r from-red-100 to-red-200 text-red-800 border border-red-300;
  }

  .glass-effect {
    @apply bg-white/10 backdrop-blur-md border border-white/20;
  }

  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .animate-pulse-slow {
    animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
