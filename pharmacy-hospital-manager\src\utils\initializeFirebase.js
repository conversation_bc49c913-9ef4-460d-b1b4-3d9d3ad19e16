// Script d'initialisation des données Firebase
import { collection, addDoc, doc, setDoc } from 'firebase/firestore';
import { createUserWithEmailAndPassword, updateProfile } from 'firebase/auth';
import { db, auth, COLLECTIONS } from '../config/firebase';

// Données de test pour les fournisseurs
const sampleSuppliers = [
  {
    name: 'PharmaCorp International',
    type: 'manufacturer',
    country: 'Allemagne',
    address: '123 Pharma Street, Berlin',
    phone: '+49 30 12345678',
    email: '<EMAIL>',
    website: 'www.pharmacorp.de',
    contactPerson: 'Dr. <PERSON>',
    licenseNumber: 'DE-PHARM-2024-001',
    taxId: 'DE123456789',
    status: 'active',
    certifications: ['ISO 9001', 'GMP', 'FDA'],
    contractStartDate: '2024-01-01',
    contractEndDate: '2026-12-31',
    paymentTerms: '30 jours',
    deliveryTerms: 'FOB Hamburg',
    rating: 4.8,
    totalContracts: 15
  },
  {
    name: 'MediLab Solutions',
    type: 'distributor',
    country: 'France',
    address: '456 Medical Avenue, Lyon',
    phone: '+33 4 12345678',
    email: '<EMAIL>',
    website: 'www.medilab.fr',
    contactPerson: 'Marie Dubois',
    licenseNumber: 'FR-DIST-2024-002',
    taxId: 'FR987654321',
    status: 'active',
    certifications: ['ISO 13485', 'GDP'],
    contractStartDate: '2024-02-01',
    contractEndDate: '2025-12-31',
    paymentTerms: '45 jours',
    deliveryTerms: 'CIF Destination',
    rating: 4.5,
    totalContracts: 8
  },
  {
    name: 'Global Pharma Import',
    type: 'importer',
    country: 'Suisse',
    address: '789 Import Plaza, Zurich',
    phone: '+41 44 1234567',
    email: '<EMAIL>',
    website: 'www.globalpharma.ch',
    contactPerson: 'Dr. Klaus Weber',
    licenseNumber: 'CH-IMP-2024-003',
    taxId: 'CHE-123.456.789',
    status: 'active',
    certifications: ['ISO 9001', 'GDP', 'WHO PQ'],
    contractStartDate: '2024-03-01',
    contractEndDate: '2027-02-28',
    paymentTerms: '60 jours',
    deliveryTerms: 'DAP Destination',
    rating: 4.2,
    totalContracts: 12
  }
];

// Données de test pour les médicaments
const sampleMedicines = [
  {
    name: 'Paracetamol',
    dosage: '500mg',
    form: 'tablet',
    manufacturer: 'PharmaCorp',
    batchNumber: '*********',
    expiryDate: '2025-12-31',
    quantity: 450,
    minQuantity: 100,
    price: 0.25,
    status: 'active'
  },
  {
    name: 'Amoxicillin',
    dosage: '250mg',
    form: 'capsule',
    manufacturer: 'MediLab',
    batchNumber: '*********',
    expiryDate: '2025-08-15',
    quantity: 78,
    minQuantity: 150,
    price: 0.45,
    status: 'low_stock'
  },
  {
    name: 'Ibuprofen',
    dosage: '400mg',
    form: 'tablet',
    manufacturer: 'Global Pharma',
    batchNumber: '*********',
    expiryDate: '2026-03-20',
    quantity: 23,
    minQuantity: 80,
    price: 0.35,
    status: 'critical'
  }
];

// Données de test pour les pharmacies
const samplePharmacies = [
  {
    name: 'Central Pharmacy',
    address: '123 Main Street, Downtown',
    phone: '+****************',
    email: '<EMAIL>',
    manager: 'Dr. John Smith',
    licenseNumber: '*********',
    status: 'active',
    operatingHours: '8:00 AM - 10:00 PM',
    totalMedicines: 1247,
    lowStockItems: 12
  },
  {
    name: 'Westside Pharmacy',
    address: '456 Oak Avenue, Westside',
    phone: '+****************',
    email: '<EMAIL>',
    manager: 'Dr. Michael Chen',
    licenseNumber: '*********',
    status: 'active',
    operatingHours: '9:00 AM - 9:00 PM',
    totalMedicines: 892,
    lowStockItems: 8
  }
];

// Données de test pour les hôpitaux
const sampleHospitals = [
  {
    name: 'City General Hospital',
    address: '100 Hospital Drive, Central District',
    phone: '+****************',
    email: '<EMAIL>',
    administrator: 'Dr. Sarah Wilson',
    licenseNumber: 'HOS2024001',
    status: 'active',
    totalBeds: 250,
    occupiedBeds: 180,
    departments: ['Emergency', 'Surgery', 'Cardiology', 'Pediatrics'],
    totalMedicines: 892
  },
  {
    name: "St. Mary's Medical Center",
    address: '200 Healthcare Boulevard, Midtown',
    phone: '+****************',
    email: '<EMAIL>',
    administrator: 'Dr. Lisa Thompson',
    licenseNumber: 'HOS2024002',
    status: 'active',
    totalBeds: 180,
    occupiedBeds: 145,
    departments: ['Maternity', 'Oncology', 'Neurology'],
    totalMedicines: 654
  }
];

// Fonction pour créer un utilisateur de démonstration
export const createDemoUser = async () => {
  try {
    // Créer l'utilisateur avec Firebase Auth
    const userCredential = await createUserWithEmailAndPassword(
      auth,
      '<EMAIL>',
      'demo123456'
    );
    
    const user = userCredential.user;
    
    // Mettre à jour le profil
    await updateProfile(user, {
      displayName: 'Utilisateur Démo'
    });
    
    // Créer le document utilisateur dans Firestore
    await setDoc(doc(db, COLLECTIONS.USERS, user.uid), {
      name: 'Utilisateur Démo',
      email: '<EMAIL>',
      role: 'admin',
      permissions: ['all'],
      department: 'Administration',
      phone: '+33 1 23 45 67 89',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    });
    
    console.log('Utilisateur de démonstration créé avec succès');
    return user;
  } catch (error) {
    if (error.code === 'auth/email-already-in-use') {
      console.log('L\'utilisateur de démonstration existe déjà');
      return null;
    }
    console.error('Erreur lors de la création de l\'utilisateur de démonstration:', error);
    throw error;
  }
};

// Fonction pour initialiser les données de test
export const initializeSampleData = async () => {
  try {
    console.log('Initialisation des données de test...');
    
    // Ajouter les fournisseurs
    console.log('Ajout des fournisseurs...');
    for (const supplier of sampleSuppliers) {
      await addDoc(collection(db, COLLECTIONS.SUPPLIERS), {
        ...supplier,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      });
    }
    
    // Ajouter les médicaments
    console.log('Ajout des médicaments...');
    for (const medicine of sampleMedicines) {
      await addDoc(collection(db, COLLECTIONS.MEDICINES), {
        ...medicine,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      });
    }
    
    // Ajouter les pharmacies
    console.log('Ajout des pharmacies...');
    for (const pharmacy of samplePharmacies) {
      await addDoc(collection(db, COLLECTIONS.PHARMACIES), {
        ...pharmacy,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      });
    }
    
    // Ajouter les hôpitaux
    console.log('Ajout des hôpitaux...');
    for (const hospital of sampleHospitals) {
      await addDoc(collection(db, COLLECTIONS.HOSPITALS), {
        ...hospital,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      });
    }
    
    console.log('Données de test initialisées avec succès !');
  } catch (error) {
    console.error('Erreur lors de l\'initialisation des données:', error);
    throw error;
  }
};

// Fonction principale d'initialisation
export const initializeFirebase = async () => {
  try {
    console.log('Initialisation de Firebase...');
    
    // Créer l'utilisateur de démonstration
    await createDemoUser();
    
    // Initialiser les données de test
    await initializeSampleData();
    
    console.log('Firebase initialisé avec succès !');
  } catch (error) {
    console.error('Erreur lors de l\'initialisation de Firebase:', error);
    throw error;
  }
};

export default initializeFirebase;
