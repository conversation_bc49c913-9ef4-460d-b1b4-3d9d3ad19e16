import React, { useState, useEffect } from 'react';
import { Save, Loader2 } from 'lucide-react';

const HospitalForm = ({ hospital, onSubmit, onCancel, isLoading = false }) => {
  const [formData, setFormData] = useState({
    name: '',
    address: '',
    phone: '',
    email: '',
    administrator: '',
    licenseNumber: '',
    totalBeds: '',
    departments: [],
    description: ''
  });

  const [errors, setErrors] = useState({});
  const [departmentInput, setDepartmentInput] = useState('');

  const availableDepartments = [
    'Emergency', 'Surgery', 'Cardiology', 'Pediatrics', 'Maternity', 
    'Oncology', 'Neurology', 'Orthopedics', 'Radiology', 'ICU',
    'Pharmacy', 'Laboratory', 'Physiotherapy', 'Psychiatry'
  ];

  useEffect(() => {
    if (hospital) {
      setFormData({
        name: hospital.name || '',
        address: hospital.address || '',
        phone: hospital.phone || '',
        email: hospital.email || '',
        administrator: hospital.administrator || '',
        licenseNumber: hospital.licenseNumber || '',
        totalBeds: hospital.totalBeds?.toString() || '',
        departments: hospital.departments || [],
        description: hospital.description || ''
      });
    }
  }, [hospital]);

  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) newErrors.name = 'Hospital name is required';
    if (!formData.address.trim()) newErrors.address = 'Address is required';
    if (!formData.phone.trim()) newErrors.phone = 'Phone number is required';
    if (!formData.email.trim()) newErrors.email = 'Email is required';
    if (!formData.administrator.trim()) newErrors.administrator = 'Administrator name is required';
    if (!formData.licenseNumber.trim()) newErrors.licenseNumber = 'License number is required';
    if (!formData.totalBeds || formData.totalBeds <= 0) newErrors.totalBeds = 'Valid number of beds is required';

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (formData.email && !emailRegex.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    // Phone validation (basic)
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    if (formData.phone && !phoneRegex.test(formData.phone.replace(/[\s\-\(\)]/g, ''))) {
      newErrors.phone = 'Please enter a valid phone number';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (validateForm()) {
      const submitData = {
        ...formData,
        totalBeds: parseInt(formData.totalBeds)
      };
      onSubmit(submitData);
    }
  };

  const handleChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const addDepartment = () => {
    if (departmentInput.trim() && !formData.departments.includes(departmentInput.trim())) {
      setFormData(prev => ({
        ...prev,
        departments: [...prev.departments, departmentInput.trim()]
      }));
      setDepartmentInput('');
    }
  };

  const removeDepartment = (dept) => {
    setFormData(prev => ({
      ...prev,
      departments: prev.departments.filter(d => d !== dept)
    }));
  };

  const addPredefinedDepartment = (dept) => {
    if (!formData.departments.includes(dept)) {
      setFormData(prev => ({
        ...prev,
        departments: [...prev.departments, dept]
      }));
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Hospital Name */}
        <div>
          <label className="label">Hospital Name *</label>
          <input
            type="text"
            className={`input ${errors.name ? 'border-red-500 focus:ring-red-500' : ''}`}
            value={formData.name}
            onChange={(e) => handleChange('name', e.target.value)}
            placeholder="e.g., City General Hospital"
          />
          {errors.name && <p className="text-red-500 text-xs mt-1">{errors.name}</p>}
        </div>

        {/* Administrator */}
        <div>
          <label className="label">Administrator *</label>
          <input
            type="text"
            className={`input ${errors.administrator ? 'border-red-500 focus:ring-red-500' : ''}`}
            value={formData.administrator}
            onChange={(e) => handleChange('administrator', e.target.value)}
            placeholder="e.g., Dr. Robert Wilson"
          />
          {errors.administrator && <p className="text-red-500 text-xs mt-1">{errors.administrator}</p>}
        </div>

        {/* Address */}
        <div className="md:col-span-2">
          <label className="label">Address *</label>
          <input
            type="text"
            className={`input ${errors.address ? 'border-red-500 focus:ring-red-500' : ''}`}
            value={formData.address}
            onChange={(e) => handleChange('address', e.target.value)}
            placeholder="e.g., 100 Medical Center Drive, Downtown"
          />
          {errors.address && <p className="text-red-500 text-xs mt-1">{errors.address}</p>}
        </div>

        {/* Phone */}
        <div>
          <label className="label">Phone *</label>
          <input
            type="tel"
            className={`input ${errors.phone ? 'border-red-500 focus:ring-red-500' : ''}`}
            value={formData.phone}
            onChange={(e) => handleChange('phone', e.target.value)}
            placeholder="e.g., +****************"
          />
          {errors.phone && <p className="text-red-500 text-xs mt-1">{errors.phone}</p>}
        </div>

        {/* Email */}
        <div>
          <label className="label">Email *</label>
          <input
            type="email"
            className={`input ${errors.email ? 'border-red-500 focus:ring-red-500' : ''}`}
            value={formData.email}
            onChange={(e) => handleChange('email', e.target.value)}
            placeholder="e.g., <EMAIL>"
          />
          {errors.email && <p className="text-red-500 text-xs mt-1">{errors.email}</p>}
        </div>

        {/* License Number */}
        <div>
          <label className="label">License Number *</label>
          <input
            type="text"
            className={`input ${errors.licenseNumber ? 'border-red-500 focus:ring-red-500' : ''}`}
            value={formData.licenseNumber}
            onChange={(e) => handleChange('licenseNumber', e.target.value)}
            placeholder="e.g., HOS2024001"
          />
          {errors.licenseNumber && <p className="text-red-500 text-xs mt-1">{errors.licenseNumber}</p>}
        </div>

        {/* Total Beds */}
        <div>
          <label className="label">Total Beds *</label>
          <input
            type="number"
            min="1"
            className={`input ${errors.totalBeds ? 'border-red-500 focus:ring-red-500' : ''}`}
            value={formData.totalBeds}
            onChange={(e) => handleChange('totalBeds', e.target.value)}
            placeholder="e.g., 250"
          />
          {errors.totalBeds && <p className="text-red-500 text-xs mt-1">{errors.totalBeds}</p>}
        </div>
      </div>

      {/* Departments */}
      <div>
        <label className="label">Departments</label>
        
        {/* Quick Add Buttons */}
        <div className="mb-3">
          <p className="text-xs text-gray-500 mb-2">Quick add common departments:</p>
          <div className="flex flex-wrap gap-2">
            {availableDepartments.map(dept => (
              <button
                key={dept}
                type="button"
                onClick={() => addPredefinedDepartment(dept)}
                disabled={formData.departments.includes(dept)}
                className={`px-3 py-1 text-xs rounded-full border transition-colors ${
                  formData.departments.includes(dept)
                    ? 'bg-gray-100 text-gray-400 border-gray-200 cursor-not-allowed'
                    : 'bg-white text-gray-700 border-gray-300 hover:bg-blue-50 hover:border-blue-300'
                }`}
              >
                {dept}
              </button>
            ))}
          </div>
        </div>

        {/* Custom Department Input */}
        <div className="flex gap-2 mb-3">
          <input
            type="text"
            className="input flex-1"
            value={departmentInput}
            onChange={(e) => setDepartmentInput(e.target.value)}
            placeholder="Add custom department..."
            onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addDepartment())}
          />
          <button
            type="button"
            onClick={addDepartment}
            className="btn-secondary"
          >
            Add
          </button>
        </div>

        {/* Selected Departments */}
        {formData.departments.length > 0 && (
          <div className="flex flex-wrap gap-2">
            {formData.departments.map(dept => (
              <span
                key={dept}
                className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800"
              >
                {dept}
                <button
                  type="button"
                  onClick={() => removeDepartment(dept)}
                  className="ml-2 text-blue-600 hover:text-blue-800"
                >
                  ×
                </button>
              </span>
            ))}
          </div>
        )}
      </div>

      {/* Description */}
      <div>
        <label className="label">Description</label>
        <textarea
          className="input min-h-[100px] resize-none"
          value={formData.description}
          onChange={(e) => handleChange('description', e.target.value)}
          placeholder="Additional information about the hospital..."
          rows={4}
        />
      </div>

      {/* Actions */}
      <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
        <button
          type="button"
          onClick={onCancel}
          className="btn-secondary"
          disabled={isLoading}
        >
          Cancel
        </button>
        <button
          type="submit"
          className="btn-primary"
          disabled={isLoading}
        >
          {isLoading ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Saving...
            </>
          ) : (
            <>
              <Save className="h-4 w-4 mr-2" />
              {hospital ? 'Update Hospital' : 'Add Hospital'}
            </>
          )}
        </button>
      </div>
    </form>
  );
};

export default HospitalForm;
