// Services Firebase pour la plateforme de gestion pharmaceutique
import {
  collection,
  doc,
  getDocs,
  getDoc,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
  startAfter,
  onSnapshot,
  serverTimestamp,
  writeBatch,
  runTransaction
} from 'firebase/firestore';

import {
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  signOut,
  updateProfile,
  sendPasswordResetEmail
} from 'firebase/auth';

import {
  ref,
  uploadBytes,
  getDownloadURL,
  deleteObject
} from 'firebase/storage';

import { db, auth, storage, COLLECTIONS } from '../config/firebase';
import { generateId, generateNumber } from '../models';

// Classe de base pour les services Firebase
class BaseFirebaseService {
  constructor(collectionName) {
    this.collectionName = collectionName;
    this.collectionRef = collection(db, collectionName);
  }

  // Obtenir tous les documents avec pagination
  async getAll(options = {}) {
    try {
      const {
        filters = [],
        orderByField = 'createdAt',
        orderDirection = 'desc',
        limitCount = 50,
        startAfterDoc = null
      } = options;

      let q = query(this.collectionRef);

      // Appliquer les filtres
      filters.forEach(filter => {
        q = query(q, where(filter.field, filter.operator, filter.value));
      });

      // Appliquer l'ordre
      q = query(q, orderBy(orderByField, orderDirection));

      // Appliquer la pagination
      if (limitCount) {
        q = query(q, limit(limitCount));
      }

      if (startAfterDoc) {
        q = query(q, startAfter(startAfterDoc));
      }

      const snapshot = await getDocs(q);
      const documents = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      return {
        data: documents,
        lastDoc: snapshot.docs[snapshot.docs.length - 1],
        hasMore: snapshot.docs.length === limitCount,
        total: snapshot.size
      };
    } catch (error) {
      console.error(`Erreur lors de la récupération des documents ${this.collectionName}:`, error);
      throw error;
    }
  }

  // Obtenir un document par ID
  async getById(id) {
    try {
      const docRef = doc(db, this.collectionName, id);
      const docSnap = await getDoc(docRef);

      if (docSnap.exists()) {
        return {
          id: docSnap.id,
          ...docSnap.data()
        };
      } else {
        throw new Error('Document non trouvé');
      }
    } catch (error) {
      console.error(`Erreur lors de la récupération du document ${id}:`, error);
      throw error;
    }
  }

  // Créer un nouveau document
  async create(data) {
    try {
      const docData = {
        ...data,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      };

      const docRef = await addDoc(this.collectionRef, docData);
      
      // Récupérer le document créé avec l'ID
      const createdDoc = await this.getById(docRef.id);
      
      return createdDoc;
    } catch (error) {
      console.error(`Erreur lors de la création du document:`, error);
      throw error;
    }
  }

  // Mettre à jour un document
  async update(id, data) {
    try {
      const docRef = doc(db, this.collectionName, id);
      const updateData = {
        ...data,
        updatedAt: serverTimestamp()
      };

      await updateDoc(docRef, updateData);
      
      // Récupérer le document mis à jour
      const updatedDoc = await this.getById(id);
      
      return updatedDoc;
    } catch (error) {
      console.error(`Erreur lors de la mise à jour du document ${id}:`, error);
      throw error;
    }
  }

  // Supprimer un document
  async delete(id) {
    try {
      const docRef = doc(db, this.collectionName, id);
      await deleteDoc(docRef);
      
      return { success: true, id };
    } catch (error) {
      console.error(`Erreur lors de la suppression du document ${id}:`, error);
      throw error;
    }
  }

  // Écouter les changements en temps réel
  onSnapshot(callback, options = {}) {
    try {
      const {
        filters = [],
        orderByField = 'createdAt',
        orderDirection = 'desc',
        limitCount = 50
      } = options;

      let q = query(this.collectionRef);

      // Appliquer les filtres
      filters.forEach(filter => {
        q = query(q, where(filter.field, filter.operator, filter.value));
      });

      // Appliquer l'ordre
      q = query(q, orderBy(orderByField, orderDirection));

      // Appliquer la limite
      if (limitCount) {
        q = query(q, limit(limitCount));
      }

      return onSnapshot(q, (snapshot) => {
        const documents = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));
        
        callback(documents);
      });
    } catch (error) {
      console.error(`Erreur lors de l'écoute des changements:`, error);
      throw error;
    }
  }

  // Recherche textuelle
  async search(searchTerm, searchFields = ['name']) {
    try {
      // Firebase ne supporte pas la recherche textuelle native
      // Nous devons récupérer tous les documents et filtrer côté client
      const allDocs = await this.getAll({ limitCount: 1000 });
      
      const filteredDocs = allDocs.data.filter(doc => {
        return searchFields.some(field => {
          const fieldValue = doc[field];
          if (typeof fieldValue === 'string') {
            return fieldValue.toLowerCase().includes(searchTerm.toLowerCase());
          }
          return false;
        });
      });

      return {
        data: filteredDocs,
        total: filteredDocs.length
      };
    } catch (error) {
      console.error(`Erreur lors de la recherche:`, error);
      throw error;
    }
  }
}

// Service pour les fournisseurs
class SupplierService extends BaseFirebaseService {
  constructor() {
    super(COLLECTIONS.SUPPLIERS);
  }

  // Méthodes spécialisées pour les fournisseurs
  async getByType(type) {
    return this.getAll({
      filters: [{ field: 'type', operator: '==', value: type }]
    });
  }

  async getByCountry(country) {
    return this.getAll({
      filters: [{ field: 'country', operator: '==', value: country }]
    });
  }

  async getActive() {
    return this.getAll({
      filters: [{ field: 'status', operator: '==', value: 'active' }]
    });
  }

  async updateRating(id, rating) {
    return this.update(id, { rating });
  }
}

// Service pour les médicaments
class MedicineService extends BaseFirebaseService {
  constructor() {
    super(COLLECTIONS.MEDICINES);
  }

  async getLowStock() {
    // Récupérer tous les médicaments et filtrer côté client
    const allMedicines = await this.getAll({ limitCount: 1000 });
    const lowStockMedicines = allMedicines.data.filter(medicine => 
      medicine.quantity <= medicine.minQuantity
    );

    return {
      data: lowStockMedicines,
      total: lowStockMedicines.length
    };
  }

  async getExpiringSoon(days = 30) {
    const expiryDate = new Date();
    expiryDate.setDate(expiryDate.getDate() + days);

    return this.getAll({
      filters: [
        { field: 'expiryDate', operator: '<=', value: expiryDate.toISOString() },
        { field: 'status', operator: '==', value: 'active' }
      ]
    });
  }
}

// Service d'authentification
class AuthService {
  // Connexion
  async signIn(email, password) {
    try {
      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      const user = userCredential.user;

      // Récupérer les informations utilisateur depuis Firestore
      const userDoc = await getDoc(doc(db, COLLECTIONS.USERS, user.uid));
      
      if (userDoc.exists()) {
        return {
          uid: user.uid,
          email: user.email,
          ...userDoc.data()
        };
      } else {
        throw new Error('Profil utilisateur non trouvé');
      }
    } catch (error) {
      console.error('Erreur de connexion:', error);
      throw error;
    }
  }

  // Inscription
  async signUp(email, password, userData) {
    try {
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      const user = userCredential.user;

      // Mettre à jour le profil
      await updateProfile(user, {
        displayName: userData.name
      });

      // Créer le document utilisateur dans Firestore
      const userDocData = {
        ...userData,
        email: user.email,
        uid: user.uid,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      };

      await addDoc(collection(db, COLLECTIONS.USERS), userDocData);

      return {
        uid: user.uid,
        email: user.email,
        ...userDocData
      };
    } catch (error) {
      console.error('Erreur d\'inscription:', error);
      throw error;
    }
  }

  // Déconnexion
  async signOut() {
    try {
      await signOut(auth);
    } catch (error) {
      console.error('Erreur de déconnexion:', error);
      throw error;
    }
  }

  // Réinitialisation du mot de passe
  async resetPassword(email) {
    try {
      await sendPasswordResetEmail(auth, email);
    } catch (error) {
      console.error('Erreur de réinitialisation:', error);
      throw error;
    }
  }

  // Obtenir l'utilisateur actuel
  getCurrentUser() {
    return auth.currentUser;
  }

  // Écouter les changements d'authentification
  onAuthStateChanged(callback) {
    return auth.onAuthStateChanged(callback);
  }
}

// Service de stockage de fichiers
class StorageService {
  // Uploader un fichier
  async uploadFile(file, path) {
    try {
      const storageRef = ref(storage, path);
      const snapshot = await uploadBytes(storageRef, file);
      const downloadURL = await getDownloadURL(snapshot.ref);
      
      return {
        url: downloadURL,
        path: path,
        size: snapshot.metadata.size,
        contentType: snapshot.metadata.contentType
      };
    } catch (error) {
      console.error('Erreur d\'upload:', error);
      throw error;
    }
  }

  // Supprimer un fichier
  async deleteFile(path) {
    try {
      const storageRef = ref(storage, path);
      await deleteObject(storageRef);
      return { success: true };
    } catch (error) {
      console.error('Erreur de suppression:', error);
      throw error;
    }
  }

  // Obtenir l'URL de téléchargement
  async getDownloadURL(path) {
    try {
      const storageRef = ref(storage, path);
      return await getDownloadURL(storageRef);
    } catch (error) {
      console.error('Erreur de récupération URL:', error);
      throw error;
    }
  }
}

// Service d'audit
class AuditService extends BaseFirebaseService {
  constructor() {
    super(COLLECTIONS.AUDIT_LOGS);
  }

  async logAction(userId, userName, action, module, entityType, entityId, oldValues = {}, newValues = {}) {
    try {
      const logData = {
        userId,
        userName,
        action,
        module,
        entityType,
        entityId,
        oldValues,
        newValues,
        ipAddress: 'localhost', // À remplacer par la vraie IP
        userAgent: navigator.userAgent,
        timestamp: serverTimestamp(),
        success: true
      };

      return await this.create(logData);
    } catch (error) {
      console.error('Erreur de log d\'audit:', error);
      throw error;
    }
  }

  async getByUser(userId) {
    return this.getAll({
      filters: [{ field: 'userId', operator: '==', value: userId }],
      orderByField: 'timestamp',
      orderDirection: 'desc'
    });
  }

  async getByModule(module) {
    return this.getAll({
      filters: [{ field: 'module', operator: '==', value: module }],
      orderByField: 'timestamp',
      orderDirection: 'desc'
    });
  }
}

// Instances des services
export const supplierService = new SupplierService();
export const medicineService = new MedicineService();
export const authService = new AuthService();
export const storageService = new StorageService();
export const auditService = new AuditService();

// Services pour les autres collections
export const pharmacyService = new BaseFirebaseService(COLLECTIONS.PHARMACIES);
export const hospitalService = new BaseFirebaseService(COLLECTIONS.HOSPITALS);
export const ammService = new BaseFirebaseService(COLLECTIONS.AMM);
export const tenderService = new BaseFirebaseService(COLLECTIONS.TENDERS);
export const centralStockService = new BaseFirebaseService(COLLECTIONS.CENTRAL_STOCK);
export const distributionService = new BaseFirebaseService(COLLECTIONS.DISTRIBUTIONS);
export const pharmacovigilanceService = new BaseFirebaseService(COLLECTIONS.PHARMACOVIGILANCE);
export const priceRegulationService = new BaseFirebaseService(COLLECTIONS.PRICE_REGULATIONS);
export const publicReportService = new BaseFirebaseService(COLLECTIONS.PUBLIC_REPORTS);

// Export par défaut
export default {
  supplier: supplierService,
  medicine: medicineService,
  pharmacy: pharmacyService,
  hospital: hospitalService,
  amm: ammService,
  tender: tenderService,
  centralStock: centralStockService,
  distribution: distributionService,
  pharmacovigilance: pharmacovigilanceService,
  priceRegulation: priceRegulationService,
  publicReport: publicReportService,
  auth: authService,
  storage: storageService,
  audit: auditService
};
