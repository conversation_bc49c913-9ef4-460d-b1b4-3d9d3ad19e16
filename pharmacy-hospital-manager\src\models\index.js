// Modèles de données pour la plateforme centralisée de gestion des médicaments

// Statuts génériques
export const STATUSES = {
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  PENDING: 'pending',
  SUSPENDED: 'suspended',
  EXPIRED: 'expired',
  APPROVED: 'approved',
  REJECTED: 'rejected',
  DRAFT: 'draft'
};

// Priorités
export const PRIORITIES = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  CRITICAL: 'critical'
};

// Types de médicaments
export const MEDICINE_FORMS = {
  TABLET: 'tablet',
  CAPSULE: 'capsule',
  SYRUP: 'syrup',
  INJECTION: 'injection',
  CREAM: 'cream',
  DROPS: 'drops',
  INHALER: 'inhaler',
  PATCH: 'patch'
};

// Modèle Fournisseur/Importateur
export const SupplierModel = {
  id: '',
  name: '',
  type: 'manufacturer', // manufacturer, distributor, importer
  country: '',
  address: '',
  phone: '',
  email: '',
  website: '',
  contactPerson: '',
  licenseNumber: '',
  taxId: '',
  status: STATUSES.ACTIVE,
  certifications: [], // ISO, GMP, etc.
  contractStartDate: '',
  contractEndDate: '',
  paymentTerms: '',
  deliveryTerms: '',
  rating: 0,
  totalContracts: 0,
  createdAt: '',
  updatedAt: ''
};

// Modèle AMM (Autorisation de Mise sur le Marché)
export const AMMModel = {
  id: '',
  ammNumber: '',
  medicineName: '',
  activeIngredient: '',
  dosage: '',
  form: '',
  manufacturer: '',
  indication: '',
  contraindications: '',
  sideEffects: '',
  dosageInstructions: '',
  storageConditions: '',
  shelfLife: '',
  packagingInfo: '',
  status: STATUSES.PENDING,
  applicationDate: '',
  approvalDate: '',
  expiryDate: '',
  renewalDate: '',
  documents: [], // Liste des documents requis
  inspector: '',
  inspectionNotes: '',
  fee: 0,
  createdAt: '',
  updatedAt: ''
};

// Modèle Appel d'offres
export const TenderModel = {
  id: '',
  tenderNumber: '',
  title: '',
  description: '',
  type: 'open', // open, restricted, negotiated
  category: '', // medicines, equipment, services
  estimatedValue: 0,
  currency: 'USD',
  publishDate: '',
  submissionDeadline: '',
  openingDate: '',
  evaluationCriteria: '',
  technicalRequirements: '',
  status: STATUSES.DRAFT,
  requestingOrganization: '',
  contactPerson: '',
  documents: [],
  bids: [], // Liste des soumissions
  selectedBid: null,
  contractValue: 0,
  createdAt: '',
  updatedAt: ''
};

// Modèle Stock Central
export const CentralStockModel = {
  id: '',
  medicineId: '',
  medicineName: '',
  batchNumber: '',
  manufacturingDate: '',
  expiryDate: '',
  quantity: 0,
  unitPrice: 0,
  totalValue: 0,
  supplierId: '',
  supplierName: '',
  warehouseLocation: '',
  storageConditions: '',
  qrCode: '',
  barcode: '',
  status: STATUSES.ACTIVE, // active, quarantine, expired, recalled
  qualityCheck: {
    status: STATUSES.PENDING,
    inspector: '',
    date: '',
    notes: '',
    approved: false
  },
  movements: [], // Historique des mouvements
  reservations: [], // Réservations pour distribution
  createdAt: '',
  updatedAt: ''
};

// Modèle Distribution
export const DistributionModel = {
  id: '',
  distributionNumber: '',
  type: 'regional', // regional, local, emergency
  sourceWarehouse: '',
  destinationType: 'pharmacy', // pharmacy, hospital, regional_depot
  destinationId: '',
  destinationName: '',
  requestedBy: '',
  approvedBy: '',
  items: [], // Liste des médicaments à distribuer
  totalValue: 0,
  priority: PRIORITIES.MEDIUM,
  requestDate: '',
  approvalDate: '',
  shippingDate: '',
  deliveryDate: '',
  status: STATUSES.PENDING,
  transportDetails: {
    vehicle: '',
    driver: '',
    trackingNumber: '',
    temperature: '',
    conditions: ''
  },
  documents: [],
  createdAt: '',
  updatedAt: ''
};

// Modèle Pharmacovigilance
export const PharmacovigilanceModel = {
  id: '',
  reportNumber: '',
  type: 'adverse_event', // adverse_event, quality_defect, medication_error
  severity: PRIORITIES.MEDIUM,
  medicineId: '',
  medicineName: '',
  batchNumber: '',
  patientInfo: {
    age: '',
    gender: '',
    weight: '',
    medicalHistory: ''
  },
  eventDescription: '',
  outcome: '',
  reporterInfo: {
    name: '',
    profession: '',
    organization: '',
    contact: ''
  },
  reportDate: '',
  followUpRequired: false,
  status: STATUSES.PENDING,
  investigation: {
    investigator: '',
    findings: '',
    recommendations: '',
    actions: []
  },
  regulatoryActions: [],
  createdAt: '',
  updatedAt: ''
};

// Modèle Régulation des Prix
export const PriceRegulationModel = {
  id: '',
  medicineId: '',
  medicineName: '',
  category: '',
  importPrice: 0,
  customsDuty: 0,
  taxes: 0,
  distributorMargin: 0,
  pharmacyMargin: 0,
  maxRetailPrice: 0,
  subsidyAmount: 0,
  patientPrice: 0,
  currency: 'USD',
  effectiveDate: '',
  reviewDate: '',
  status: STATUSES.ACTIVE,
  justification: '',
  approvedBy: '',
  priceHistory: [],
  createdAt: '',
  updatedAt: ''
};

// Modèle Interface Publique
export const PublicReportModel = {
  id: '',
  reportType: 'stock_shortage', // stock_shortage, quality_issue, price_complaint
  medicineId: '',
  medicineName: '',
  reporterInfo: {
    name: '',
    phone: '',
    email: '',
    location: ''
  },
  description: '',
  urgency: PRIORITIES.MEDIUM,
  status: STATUSES.PENDING,
  assignedTo: '',
  response: '',
  responseDate: '',
  publicVisible: true,
  createdAt: '',
  updatedAt: ''
};

// Modèle Audit et Reporting
export const AuditLogModel = {
  id: '',
  userId: '',
  userName: '',
  action: '',
  module: '',
  entityType: '',
  entityId: '',
  oldValues: {},
  newValues: {},
  ipAddress: '',
  userAgent: '',
  timestamp: '',
  success: true,
  errorMessage: ''
};

// Fonctions utilitaires pour la validation
export const validateModel = (model, data) => {
  const errors = {};
  
  // Validation générique basée sur le modèle
  Object.keys(model).forEach(key => {
    if (model[key] === '' && !data[key]) {
      errors[key] = `${key} is required`;
    }
  });
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
};

// Fonctions de génération d'ID
export const generateId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
};

export const generateNumber = (prefix) => {
  const timestamp = Date.now().toString().slice(-6);
  const random = Math.random().toString(36).substr(2, 4).toUpperCase();
  return `${prefix}${timestamp}${random}`;
};
