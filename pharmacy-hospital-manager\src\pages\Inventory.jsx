import React, { useState, forwardRef, useImperativeHandle } from "react";
import {
  Package,
  AlertTriangle,
  Calendar,
  TrendingDown,
  Search,
  Filter,
  Download,
} from "lucide-react";

const Inventory = forwardRef((props, ref) => {
  const [activeTab, setActiveTab] = useState("overview");
  const [searchTerm, setSearchTerm] = useState("");

  const inventoryData = [
    {
      id: 1,
      medicine: "Paracetamol 500mg",
      location: "Central Pharmacy",
      currentStock: 450,
      minStock: 100,
      maxStock: 1000,
      lastRestocked: "2024-01-10",
      expiryDate: "2025-12-31",
      batchNumber: "*********",
      status: "good",
    },
    {
      id: 2,
      medicine: "Amoxicillin 250mg",
      location: "Central Pharmacy",
      currentStock: 78,
      minStock: 150,
      maxStock: 500,
      lastRestocked: "2024-01-08",
      expiryDate: "2025-08-15",
      batchNumber: "*********",
      status: "low",
    },
    {
      id: 3,
      medicine: "Ibuprofen 400mg",
      location: "Westside Pharmacy",
      currentStock: 23,
      minStock: 80,
      maxStock: 300,
      lastRestocked: "2024-01-05",
      expiryDate: "2026-03-20",
      batchNumber: "HP2024003",
      status: "critical",
    },
    {
      id: 4,
      medicine: "Aspirin 75mg",
      location: "City General Hospital",
      currentStock: 156,
      minStock: 200,
      maxStock: 600,
      lastRestocked: "2024-01-12",
      expiryDate: "2024-06-30",
      batchNumber: "AS2024004",
      status: "expiring",
    },
  ];

  const getStatusBadge = (status) => {
    switch (status) {
      case "good":
        return <span className="status-active">Good Stock</span>;
      case "low":
        return <span className="status-warning">Low Stock</span>;
      case "critical":
        return <span className="status-danger">Critical</span>;
      case "expiring":
        return <span className="status-warning">Expiring Soon</span>;
      default:
        return <span className="status-inactive">Unknown</span>;
    }
  };

  const getStockLevel = (current, min, max) => {
    const percentage = (current / max) * 100;
    let color = "bg-success-500";
    if (current <= min) color = "bg-danger-500";
    else if (current <= min * 1.5) color = "bg-warning-500";

    return { percentage, color };
  };

  const filteredInventory = inventoryData.filter(
    (item) =>
      item.medicine.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.location.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const stats = {
    totalItems: inventoryData.length,
    lowStock: inventoryData.filter(
      (item) => item.status === "low" || item.status === "critical"
    ).length,
    expiringSoon: inventoryData.filter((item) => item.status === "expiring")
      .length,
    totalValue: inventoryData.reduce(
      (sum, item) => sum + item.currentStock * 0.5,
      0
    ),
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">
            Inventory Management
          </h1>
          <p className="text-gray-600">
            Monitor stock levels and manage inventory across all locations
          </p>
        </div>
        <button className="btn-secondary">
          <Download className="h-4 w-4 mr-2" />
          Export Report
        </button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="card">
          <div className="flex items-center">
            <Package className="h-8 w-8 text-primary-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Items</p>
              <p className="text-2xl font-semibold text-gray-900">
                {stats.totalItems}
              </p>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="flex items-center">
            <TrendingDown className="h-8 w-8 text-warning-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Low Stock</p>
              <p className="text-2xl font-semibold text-warning-600">
                {stats.lowStock}
              </p>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="flex items-center">
            <Calendar className="h-8 w-8 text-danger-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Expiring Soon</p>
              <p className="text-2xl font-semibold text-danger-600">
                {stats.expiringSoon}
              </p>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="flex items-center">
            <Package className="h-8 w-8 text-success-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Value</p>
              <p className="text-2xl font-semibold text-success-600">
                ${stats.totalValue.toFixed(2)}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: "overview", name: "Overview" },
            { id: "alerts", name: "Alerts" },
            { id: "reports", name: "Reports" },
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.id
                  ? "border-primary-500 text-primary-600"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              }`}
            >
              {tab.name}
            </button>
          ))}
        </nav>
      </div>

      {/* Search and Filter */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <input
            type="text"
            placeholder="Search inventory..."
            className="input pl-10"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <button className="btn-secondary">
          <Filter className="h-4 w-4 mr-2" />
          Filter
        </button>
      </div>

      {/* Inventory Table */}
      {activeTab === "overview" && (
        <div className="card overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Medicine
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Location
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Stock Level
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Expiry Date
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredInventory.map((item) => {
                  const stockLevel = getStockLevel(
                    item.currentStock,
                    item.minStock,
                    item.maxStock
                  );
                  return (
                    <tr key={item.id}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {item.medicine}
                          </div>
                          <div className="text-sm text-gray-500">
                            Batch: {item.batchNumber}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {item.location}
                        </div>
                        <div className="text-sm text-gray-500">
                          Last restocked: {item.lastRestocked}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {item.currentStock} units
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                          <div
                            className={`h-2 rounded-full ${stockLevel.color}`}
                            style={{
                              width: `${Math.min(stockLevel.percentage, 100)}%`,
                            }}
                          ></div>
                        </div>
                        <div className="text-xs text-gray-500 mt-1">
                          Min: {item.minStock} | Max: {item.maxStock}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {item.expiryDate}
                        </div>
                        <div className="text-xs text-gray-500">
                          {Math.ceil(
                            (new Date(item.expiryDate) - new Date()) /
                              (1000 * 60 * 60 * 24)
                          )}{" "}
                          days
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {getStatusBadge(item.status)}
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Alerts Tab */}
      {activeTab === "alerts" && (
        <div className="space-y-4">
          <div className="card">
            <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
              <AlertTriangle className="h-5 w-5 text-warning-500 mr-2" />
              Stock Alerts
            </h3>
            <div className="space-y-3">
              {inventoryData
                .filter(
                  (item) => item.status === "low" || item.status === "critical"
                )
                .map((item) => (
                  <div
                    key={item.id}
                    className="flex items-center justify-between p-3 bg-warning-50 rounded-lg"
                  >
                    <div>
                      <p className="text-sm font-medium text-gray-900">
                        {item.medicine}
                      </p>
                      <p className="text-sm text-gray-600">
                        {item.location} - Current: {item.currentStock}, Min:{" "}
                        {item.minStock}
                      </p>
                    </div>
                    <span
                      className={
                        item.status === "critical"
                          ? "status-danger"
                          : "status-warning"
                      }
                    >
                      {item.status === "critical" ? "Critical" : "Low Stock"}
                    </span>
                  </div>
                ))}
            </div>
          </div>

          <div className="card">
            <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
              <Calendar className="h-5 w-5 text-danger-500 mr-2" />
              Expiry Alerts
            </h3>
            <div className="space-y-3">
              {inventoryData
                .filter((item) => item.status === "expiring")
                .map((item) => (
                  <div
                    key={item.id}
                    className="flex items-center justify-between p-3 bg-danger-50 rounded-lg"
                  >
                    <div>
                      <p className="text-sm font-medium text-gray-900">
                        {item.medicine}
                      </p>
                      <p className="text-sm text-gray-600">
                        {item.location} - Expires: {item.expiryDate}
                      </p>
                    </div>
                    <span className="status-warning">Expiring Soon</span>
                  </div>
                ))}
            </div>
          </div>
        </div>
      )}

      {/* Reports Tab */}
      {activeTab === "reports" && (
        <div className="card">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            Inventory Reports
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <button className="btn-secondary justify-start">
              <Download className="h-4 w-4 mr-2" />
              Stock Level Report
            </button>
            <button className="btn-secondary justify-start">
              <Download className="h-4 w-4 mr-2" />
              Expiry Report
            </button>
            <button className="btn-secondary justify-start">
              <Download className="h-4 w-4 mr-2" />
              Movement Report
            </button>
            <button className="btn-secondary justify-start">
              <Download className="h-4 w-4 mr-2" />
              Valuation Report
            </button>
          </div>
        </div>
      )}
    </div>
  );
});

Inventory.displayName = "Inventory";

export default Inventory;
