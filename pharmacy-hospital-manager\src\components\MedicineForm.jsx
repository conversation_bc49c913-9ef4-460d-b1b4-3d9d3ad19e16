import React, { useState, useEffect } from 'react';
import { Save, Loader2 } from 'lucide-react';

const MedicineForm = ({ medicine, onSubmit, onCancel, isLoading = false }) => {
  const [formData, setFormData] = useState({
    name: '',
    dosage: '',
    form: 'Tablet',
    manufacturer: '',
    batchNumber: '',
    expiryDate: '',
    quantity: '',
    minQuantity: '',
    price: '',
    description: ''
  });

  const [errors, setErrors] = useState({});

  useEffect(() => {
    if (medicine) {
      setFormData({
        name: medicine.name || '',
        dosage: medicine.dosage || '',
        form: medicine.form || 'Tablet',
        manufacturer: medicine.manufacturer || '',
        batchNumber: medicine.batchNumber || '',
        expiryDate: medicine.expiryDate || '',
        quantity: medicine.quantity?.toString() || '',
        minQuantity: medicine.minQuantity?.toString() || '',
        price: medicine.price?.toString() || '',
        description: medicine.description || ''
      });
    }
  }, [medicine]);

  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) newErrors.name = 'Medicine name is required';
    if (!formData.dosage.trim()) newErrors.dosage = 'Dosage is required';
    if (!formData.manufacturer.trim()) newErrors.manufacturer = 'Manufacturer is required';
    if (!formData.batchNumber.trim()) newErrors.batchNumber = 'Batch number is required';
    if (!formData.expiryDate) newErrors.expiryDate = 'Expiry date is required';
    if (!formData.quantity || formData.quantity <= 0) newErrors.quantity = 'Valid quantity is required';
    if (!formData.minQuantity || formData.minQuantity <= 0) newErrors.minQuantity = 'Valid minimum quantity is required';
    if (!formData.price || formData.price <= 0) newErrors.price = 'Valid price is required';

    // Check if expiry date is in the future
    if (formData.expiryDate && new Date(formData.expiryDate) <= new Date()) {
      newErrors.expiryDate = 'Expiry date must be in the future';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (validateForm()) {
      const submitData = {
        ...formData,
        quantity: parseInt(formData.quantity),
        minQuantity: parseInt(formData.minQuantity),
        price: parseFloat(formData.price)
      };
      onSubmit(submitData);
    }
  };

  const handleChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Medicine Name */}
        <div>
          <label className="label">Medicine Name *</label>
          <input
            type="text"
            className={`input ${errors.name ? 'border-red-500 focus:ring-red-500' : ''}`}
            value={formData.name}
            onChange={(e) => handleChange('name', e.target.value)}
            placeholder="e.g., Paracetamol"
          />
          {errors.name && <p className="text-red-500 text-xs mt-1">{errors.name}</p>}
        </div>

        {/* Dosage */}
        <div>
          <label className="label">Dosage *</label>
          <input
            type="text"
            className={`input ${errors.dosage ? 'border-red-500 focus:ring-red-500' : ''}`}
            value={formData.dosage}
            onChange={(e) => handleChange('dosage', e.target.value)}
            placeholder="e.g., 500mg"
          />
          {errors.dosage && <p className="text-red-500 text-xs mt-1">{errors.dosage}</p>}
        </div>

        {/* Form */}
        <div>
          <label className="label">Form *</label>
          <select
            className="input"
            value={formData.form}
            onChange={(e) => handleChange('form', e.target.value)}
          >
            <option value="Tablet">Tablet</option>
            <option value="Capsule">Capsule</option>
            <option value="Syrup">Syrup</option>
            <option value="Injection">Injection</option>
            <option value="Cream">Cream</option>
            <option value="Drops">Drops</option>
            <option value="Inhaler">Inhaler</option>
          </select>
        </div>

        {/* Manufacturer */}
        <div>
          <label className="label">Manufacturer *</label>
          <input
            type="text"
            className={`input ${errors.manufacturer ? 'border-red-500 focus:ring-red-500' : ''}`}
            value={formData.manufacturer}
            onChange={(e) => handleChange('manufacturer', e.target.value)}
            placeholder="e.g., PharmaCorp"
          />
          {errors.manufacturer && <p className="text-red-500 text-xs mt-1">{errors.manufacturer}</p>}
        </div>

        {/* Batch Number */}
        <div>
          <label className="label">Batch Number *</label>
          <input
            type="text"
            className={`input ${errors.batchNumber ? 'border-red-500 focus:ring-red-500' : ''}`}
            value={formData.batchNumber}
            onChange={(e) => handleChange('batchNumber', e.target.value)}
            placeholder="e.g., *********"
          />
          {errors.batchNumber && <p className="text-red-500 text-xs mt-1">{errors.batchNumber}</p>}
        </div>

        {/* Expiry Date */}
        <div>
          <label className="label">Expiry Date *</label>
          <input
            type="date"
            className={`input ${errors.expiryDate ? 'border-red-500 focus:ring-red-500' : ''}`}
            value={formData.expiryDate}
            onChange={(e) => handleChange('expiryDate', e.target.value)}
          />
          {errors.expiryDate && <p className="text-red-500 text-xs mt-1">{errors.expiryDate}</p>}
        </div>

        {/* Quantity */}
        <div>
          <label className="label">Quantity *</label>
          <input
            type="number"
            min="1"
            className={`input ${errors.quantity ? 'border-red-500 focus:ring-red-500' : ''}`}
            value={formData.quantity}
            onChange={(e) => handleChange('quantity', e.target.value)}
            placeholder="e.g., 100"
          />
          {errors.quantity && <p className="text-red-500 text-xs mt-1">{errors.quantity}</p>}
        </div>

        {/* Minimum Quantity */}
        <div>
          <label className="label">Minimum Quantity *</label>
          <input
            type="number"
            min="1"
            className={`input ${errors.minQuantity ? 'border-red-500 focus:ring-red-500' : ''}`}
            value={formData.minQuantity}
            onChange={(e) => handleChange('minQuantity', e.target.value)}
            placeholder="e.g., 20"
          />
          {errors.minQuantity && <p className="text-red-500 text-xs mt-1">{errors.minQuantity}</p>}
        </div>

        {/* Price */}
        <div>
          <label className="label">Price per Unit ($) *</label>
          <input
            type="number"
            step="0.01"
            min="0.01"
            className={`input ${errors.price ? 'border-red-500 focus:ring-red-500' : ''}`}
            value={formData.price}
            onChange={(e) => handleChange('price', e.target.value)}
            placeholder="e.g., 0.25"
          />
          {errors.price && <p className="text-red-500 text-xs mt-1">{errors.price}</p>}
        </div>
      </div>

      {/* Description */}
      <div>
        <label className="label">Description</label>
        <textarea
          className="input min-h-[100px] resize-none"
          value={formData.description}
          onChange={(e) => handleChange('description', e.target.value)}
          placeholder="Additional information about the medicine..."
          rows={4}
        />
      </div>

      {/* Actions */}
      <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
        <button
          type="button"
          onClick={onCancel}
          className="btn-secondary"
          disabled={isLoading}
        >
          Cancel
        </button>
        <button
          type="submit"
          className="btn-primary"
          disabled={isLoading}
        >
          {isLoading ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Saving...
            </>
          ) : (
            <>
              <Save className="h-4 w-4 mr-2" />
              {medicine ? 'Update Medicine' : 'Add Medicine'}
            </>
          )}
        </button>
      </div>
    </form>
  );
};

export default MedicineForm;
