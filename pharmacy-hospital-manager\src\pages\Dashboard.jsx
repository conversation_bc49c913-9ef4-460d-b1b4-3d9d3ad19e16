import React from "react";
import {
  Pill,
  Building2,
  Hospital,
  Package,
  AlertTriangle,
  TrendingUp,
  Users,
  Activity,
  ArrowUpRight,
  ArrowDownRight,
  Clock,
  CheckCircle,
  Plus,
  BarChart3,
} from "lucide-react";

const Dashboard = () => {
  // Mock data - in a real app, this would come from an API
  const stats = [
    {
      name: "Total Medicines",
      value: "1,247",
      change: "+12%",
      changeType: "positive",
      icon: Pill,
    },
    {
      name: "Active Pharmacies",
      value: "23",
      change: "+2",
      changeType: "positive",
      icon: Building2,
    },
    {
      name: "Partner Hospitals",
      value: "8",
      change: "+1",
      changeType: "positive",
      icon: Hospital,
    },
    {
      name: "Low Stock Items",
      value: "34",
      change: "+8",
      changeType: "negative",
      icon: AlertTriangle,
    },
  ];

  const recentActivities = [
    {
      id: 1,
      type: "transfer",
      message: "Medicine transfer from Central Pharmacy to City Hospital",
      time: "2 hours ago",
      status: "completed",
    },
    {
      id: 2,
      type: "stock",
      message: "Low stock alert: Paracetamol 500mg",
      time: "4 hours ago",
      status: "warning",
    },
    {
      id: 3,
      type: "expiry",
      message: "Expiry alert: 15 medicines expiring in 30 days",
      time: "6 hours ago",
      status: "warning",
    },
    {
      id: 4,
      type: "delivery",
      message: "New stock delivery received at Main Pharmacy",
      time: "1 day ago",
      status: "completed",
    },
  ];

  const lowStockMedicines = [
    {
      name: "Paracetamol 500mg",
      current: 45,
      minimum: 100,
      status: "critical",
    },
    { name: "Amoxicillin 250mg", current: 78, minimum: 150, status: "low" },
    { name: "Ibuprofen 400mg", current: 23, minimum: 80, status: "critical" },
    { name: "Aspirin 75mg", current: 156, minimum: 200, status: "low" },
  ];

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
          <p className="mt-2 text-gray-600">
            Welcome back! Here's what's happening with your pharmacy network.
          </p>
        </div>
        <div className="mt-4 sm:mt-0 flex space-x-3">
          <button className="btn-secondary">
            <BarChart3 className="h-4 w-4 mr-2" />
            View Reports
          </button>
          <button className="btn-primary">
            <Plus className="h-4 w-4 mr-2" />
            Quick Add
          </button>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat, index) => {
          const Icon = stat.icon;
          const isPositive = stat.changeType === "positive";
          const TrendIcon = isPositive ? ArrowUpRight : ArrowDownRight;

          return (
            <div
              key={stat.name}
              className={`card-interactive animate-fade-in`}
              style={{ animationDelay: `${index * 100}ms` }}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <div
                    className={`p-3 rounded-xl ${
                      stat.name === "Total Medicines"
                        ? "bg-blue-100"
                        : stat.name === "Active Pharmacies"
                        ? "bg-green-100"
                        : stat.name === "Partner Hospitals"
                        ? "bg-purple-100"
                        : "bg-red-100"
                    }`}
                  >
                    <Icon
                      className={`h-6 w-6 ${
                        stat.name === "Total Medicines"
                          ? "text-blue-600"
                          : stat.name === "Active Pharmacies"
                          ? "text-green-600"
                          : stat.name === "Partner Hospitals"
                          ? "text-purple-600"
                          : "text-red-600"
                      }`}
                    />
                  </div>
                </div>
                <div
                  className={`flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium ${
                    isPositive
                      ? "bg-green-100 text-green-700"
                      : "bg-red-100 text-red-700"
                  }`}
                >
                  <TrendIcon className="h-3 w-3" />
                  <span>{stat.change}</span>
                </div>
              </div>
              <div className="mt-4">
                <p className="text-sm font-medium text-gray-600">{stat.name}</p>
                <p className="text-3xl font-bold text-gray-900 mt-1">
                  {stat.value}
                </p>
              </div>
            </div>
          );
        })}
      </div>

      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        {/* Recent Activities */}
        <div className="card">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">
              Recent Activities
            </h3>
            <Activity className="h-5 w-5 text-blue-500" />
          </div>
          <div className="space-y-4">
            {recentActivities.map((activity, index) => (
              <div
                key={activity.id}
                className="flex items-start space-x-4 p-3 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <div className="flex-shrink-0 mt-1">
                  {activity.status === "completed" ? (
                    <CheckCircle className="h-5 w-5 text-green-500" />
                  ) : (
                    <Clock className="h-5 w-5 text-amber-500" />
                  )}
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900">
                    {activity.message}
                  </p>
                  <p className="text-xs text-gray-500 mt-1 flex items-center">
                    <Clock className="h-3 w-3 mr-1" />
                    {activity.time}
                  </p>
                </div>
              </div>
            ))}
          </div>
          <div className="mt-4 pt-4 border-t border-gray-200">
            <button className="text-sm text-blue-600 hover:text-blue-700 font-medium">
              View all activities →
            </button>
          </div>
        </div>

        {/* Low Stock Alert */}
        <div className="card">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">
              Low Stock Alerts
            </h3>
            <AlertTriangle className="h-5 w-5 text-red-500" />
          </div>
          <div className="space-y-4">
            {lowStockMedicines.map((medicine, index) => {
              const percentage = (medicine.current / medicine.minimum) * 100;
              const isCritical = medicine.status === "critical";

              return (
                <div
                  key={index}
                  className="p-3 rounded-lg border border-gray-200 hover:border-gray-300 transition-colors"
                >
                  <div className="flex items-center justify-between mb-2">
                    <p className="text-sm font-medium text-gray-900">
                      {medicine.name}
                    </p>
                    <span
                      className={`status-badge ${
                        isCritical ? "status-danger" : "status-warning"
                      }`}
                    >
                      {medicine.status}
                    </span>
                  </div>
                  <div className="flex items-center justify-between text-xs text-gray-500 mb-2">
                    <span>Current: {medicine.current}</span>
                    <span>Min: {medicine.minimum}</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full ${
                        isCritical ? "bg-red-500" : "bg-amber-500"
                      }`}
                      style={{ width: `${Math.min(percentage, 100)}%` }}
                    ></div>
                  </div>
                </div>
              );
            })}
          </div>
          <div className="mt-4 pt-4 border-t border-gray-200">
            <button className="text-sm text-red-600 hover:text-red-700 font-medium">
              View all alerts →
            </button>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="card">
        <h3 className="text-lg font-semibold text-gray-900 mb-6">
          Quick Actions
        </h3>
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
          <button className="btn-primary group">
            <Pill className="h-4 w-4 mr-2 group-hover:scale-110 transition-transform" />
            Add Medicine
          </button>
          <button className="btn-secondary group">
            <Package className="h-4 w-4 mr-2 group-hover:scale-110 transition-transform" />
            Update Inventory
          </button>
          <button className="btn-secondary group">
            <TrendingUp className="h-4 w-4 mr-2 group-hover:scale-110 transition-transform" />
            View Workflow
          </button>
          <button className="btn-secondary group">
            <Users className="h-4 w-4 mr-2 group-hover:scale-110 transition-transform" />
            Manage Facilities
          </button>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
