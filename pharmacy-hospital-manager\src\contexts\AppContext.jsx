import React, { createContext, useContext, useReducer, useEffect } from "react";
import { STATUSES, PRIORITIES, generateId, generateNumber } from "../models";
import firebaseServices, { auditService } from "../services/firebaseService";
import { useAuth } from "../hooks/useAuth";

// État initial de l'application
const initialState = {
  // Données existantes
  medicines: [],
  pharmacies: [],
  hospitals: [],

  // Nouvelles données pour la plateforme centralisée
  suppliers: [],
  amm: [],
  tenders: [],
  centralStock: [],
  distributions: [],
  pharmacovigilance: [],
  priceRegulations: [],
  publicReports: [],
  auditLogs: [],

  // État de l'interface utilisateur
  loading: false,
  error: null,
  notifications: [],

  // Utilisateur et authentification
  user: {
    id: "admin",
    name: "Administrator",
    role: "admin",
    permissions: ["all"],
  },

  // Paramètres système
  settings: {
    systemName: "PharmaCare Management System",
    currency: "USD",
    language: "fr",
    timezone: "UTC+0",
    lowStockThreshold: 20,
    expiryWarningDays: 30,
  },
};

// Types d'actions
const ActionTypes = {
  // Actions génériques
  SET_LOADING: "SET_LOADING",
  SET_ERROR: "SET_ERROR",
  CLEAR_ERROR: "CLEAR_ERROR",
  ADD_NOTIFICATION: "ADD_NOTIFICATION",
  REMOVE_NOTIFICATION: "REMOVE_NOTIFICATION",

  // Actions pour les médicaments
  SET_MEDICINES: "SET_MEDICINES",
  ADD_MEDICINE: "ADD_MEDICINE",
  UPDATE_MEDICINE: "UPDATE_MEDICINE",
  DELETE_MEDICINE: "DELETE_MEDICINE",

  // Actions pour les pharmacies
  SET_PHARMACIES: "SET_PHARMACIES",
  ADD_PHARMACY: "ADD_PHARMACY",
  UPDATE_PHARMACY: "UPDATE_PHARMACY",
  DELETE_PHARMACY: "DELETE_PHARMACY",

  // Actions pour les hôpitaux
  SET_HOSPITALS: "SET_HOSPITALS",
  ADD_HOSPITAL: "ADD_HOSPITAL",
  UPDATE_HOSPITAL: "UPDATE_HOSPITAL",
  DELETE_HOSPITAL: "DELETE_HOSPITAL",

  // Actions pour les fournisseurs
  SET_SUPPLIERS: "SET_SUPPLIERS",
  ADD_SUPPLIER: "ADD_SUPPLIER",
  UPDATE_SUPPLIER: "UPDATE_SUPPLIER",
  DELETE_SUPPLIER: "DELETE_SUPPLIER",

  // Actions pour AMM
  SET_AMM: "SET_AMM",
  ADD_AMM: "ADD_AMM",
  UPDATE_AMM: "UPDATE_AMM",
  DELETE_AMM: "DELETE_AMM",

  // Actions pour les appels d'offres
  SET_TENDERS: "SET_TENDERS",
  ADD_TENDER: "ADD_TENDER",
  UPDATE_TENDER: "UPDATE_TENDER",
  DELETE_TENDER: "DELETE_TENDER",

  // Actions pour le stock central
  SET_CENTRAL_STOCK: "SET_CENTRAL_STOCK",
  ADD_CENTRAL_STOCK: "ADD_CENTRAL_STOCK",
  UPDATE_CENTRAL_STOCK: "UPDATE_CENTRAL_STOCK",
  DELETE_CENTRAL_STOCK: "DELETE_CENTRAL_STOCK",

  // Actions pour la distribution
  SET_DISTRIBUTIONS: "SET_DISTRIBUTIONS",
  ADD_DISTRIBUTION: "ADD_DISTRIBUTION",
  UPDATE_DISTRIBUTION: "UPDATE_DISTRIBUTION",
  DELETE_DISTRIBUTION: "DELETE_DISTRIBUTION",

  // Actions pour la pharmacovigilance
  SET_PHARMACOVIGILANCE: "SET_PHARMACOVIGILANCE",
  ADD_PHARMACOVIGILANCE: "ADD_PHARMACOVIGILANCE",
  UPDATE_PHARMACOVIGILANCE: "UPDATE_PHARMACOVIGILANCE",
  DELETE_PHARMACOVIGILANCE: "DELETE_PHARMACOVIGILANCE",

  // Actions pour la régulation des prix
  SET_PRICE_REGULATIONS: "SET_PRICE_REGULATIONS",
  ADD_PRICE_REGULATION: "ADD_PRICE_REGULATION",
  UPDATE_PRICE_REGULATION: "UPDATE_PRICE_REGULATION",
  DELETE_PRICE_REGULATION: "DELETE_PRICE_REGULATION",

  // Actions pour les rapports publics
  SET_PUBLIC_REPORTS: "SET_PUBLIC_REPORTS",
  ADD_PUBLIC_REPORT: "ADD_PUBLIC_REPORT",
  UPDATE_PUBLIC_REPORT: "UPDATE_PUBLIC_REPORT",
  DELETE_PUBLIC_REPORT: "DELETE_PUBLIC_REPORT",

  // Actions pour les logs d'audit
  ADD_AUDIT_LOG: "ADD_AUDIT_LOG",
  SET_AUDIT_LOGS: "SET_AUDIT_LOGS",
};

// Réducteur principal
const appReducer = (state, action) => {
  switch (action.type) {
    case ActionTypes.SET_LOADING:
      return { ...state, loading: action.payload };

    case ActionTypes.SET_ERROR:
      return { ...state, error: action.payload, loading: false };

    case ActionTypes.CLEAR_ERROR:
      return { ...state, error: null };

    case ActionTypes.ADD_NOTIFICATION:
      return {
        ...state,
        notifications: [
          ...state.notifications,
          {
            id: generateId(),
            ...action.payload,
            timestamp: new Date().toISOString(),
          },
        ],
      };

    case ActionTypes.REMOVE_NOTIFICATION:
      return {
        ...state,
        notifications: state.notifications.filter(
          (n) => n.id !== action.payload
        ),
      };

    // Actions génériques pour tous les types de données
    case ActionTypes.SET_MEDICINES:
      return { ...state, medicines: action.payload };
    case ActionTypes.ADD_MEDICINE:
      return {
        ...state,
        medicines: [
          ...state.medicines,
          { ...action.payload, id: generateId() },
        ],
      };
    case ActionTypes.UPDATE_MEDICINE:
      return {
        ...state,
        medicines: state.medicines.map((item) =>
          item.id === action.payload.id ? { ...item, ...action.payload } : item
        ),
      };
    case ActionTypes.DELETE_MEDICINE:
      return {
        ...state,
        medicines: state.medicines.filter((item) => item.id !== action.payload),
      };

    case ActionTypes.SET_PHARMACIES:
      return { ...state, pharmacies: action.payload };
    case ActionTypes.ADD_PHARMACY:
      return {
        ...state,
        pharmacies: [
          ...state.pharmacies,
          { ...action.payload, id: generateId() },
        ],
      };
    case ActionTypes.UPDATE_PHARMACY:
      return {
        ...state,
        pharmacies: state.pharmacies.map((item) =>
          item.id === action.payload.id ? { ...item, ...action.payload } : item
        ),
      };
    case ActionTypes.DELETE_PHARMACY:
      return {
        ...state,
        pharmacies: state.pharmacies.filter(
          (item) => item.id !== action.payload
        ),
      };

    case ActionTypes.SET_HOSPITALS:
      return { ...state, hospitals: action.payload };
    case ActionTypes.ADD_HOSPITAL:
      return {
        ...state,
        hospitals: [
          ...state.hospitals,
          { ...action.payload, id: generateId() },
        ],
      };
    case ActionTypes.UPDATE_HOSPITAL:
      return {
        ...state,
        hospitals: state.hospitals.map((item) =>
          item.id === action.payload.id ? { ...item, ...action.payload } : item
        ),
      };
    case ActionTypes.DELETE_HOSPITAL:
      return {
        ...state,
        hospitals: state.hospitals.filter((item) => item.id !== action.payload),
      };

    case ActionTypes.SET_SUPPLIERS:
      return { ...state, suppliers: action.payload };
    case ActionTypes.ADD_SUPPLIER:
      return {
        ...state,
        suppliers: [
          ...state.suppliers,
          { ...action.payload, id: generateId() },
        ],
      };
    case ActionTypes.UPDATE_SUPPLIER:
      return {
        ...state,
        suppliers: state.suppliers.map((item) =>
          item.id === action.payload.id ? { ...item, ...action.payload } : item
        ),
      };
    case ActionTypes.DELETE_SUPPLIER:
      return {
        ...state,
        suppliers: state.suppliers.filter((item) => item.id !== action.payload),
      };

    case ActionTypes.ADD_AUDIT_LOG:
      return {
        ...state,
        auditLogs: [
          ...state.auditLogs,
          {
            ...action.payload,
            id: generateId(),
            timestamp: new Date().toISOString(),
          },
        ],
      };

    default:
      return state;
  }
};

// Contexte
const AppContext = createContext();

// Provider
export const AppProvider = ({ children }) => {
  const [state, dispatch] = useReducer(appReducer, initialState);

  // Fonctions utilitaires
  const showNotification = (message, type = "info") => {
    dispatch({
      type: ActionTypes.ADD_NOTIFICATION,
      payload: { message, type },
    });
  };

  const hideNotification = (id) => {
    dispatch({
      type: ActionTypes.REMOVE_NOTIFICATION,
      payload: id,
    });
  };

  const logAction = async (
    action,
    module,
    entityType,
    entityId,
    oldValues = {},
    newValues = {}
  ) => {
    try {
      // Log dans Firebase
      await auditService.logAction(
        state.user.id,
        state.user.name,
        action,
        module,
        entityType,
        entityId,
        oldValues,
        newValues
      );

      // Log dans l'état local pour l'affichage immédiat
      dispatch({
        type: ActionTypes.ADD_AUDIT_LOG,
        payload: {
          userId: state.user.id,
          userName: state.user.name,
          action,
          module,
          entityType,
          entityId,
          oldValues,
          newValues,
          ipAddress: "localhost",
          userAgent: navigator.userAgent,
          success: true,
        },
      });
    } catch (error) {
      console.error("Erreur lors du log d'audit:", error);
    }
  };

  // Valeur du contexte
  const value = {
    state,
    dispatch,
    actions: ActionTypes,
    showNotification,
    hideNotification,
    logAction,
    // Services Firebase
    services: firebaseServices,
  };

  return <AppContext.Provider value={value}>{children}</AppContext.Provider>;
};

// Hook personnalisé
export const useApp = () => {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error("useApp must be used within an AppProvider");
  }
  return context;
};

export default AppContext;
