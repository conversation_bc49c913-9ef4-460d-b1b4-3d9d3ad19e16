import React, {
  useState,
  forwardRef,
  useImperativeHandle,
  useEffect,
} from "react";
import {
  Plus,
  Search,
  Filter,
  Edit,
  Trash2,
  Eye,
  Download,
  Building,
  Globe,
  Phone,
  Mail,
  Star,
  Award,
  Calendar,
  MapPin,
} from "lucide-react";
import Modal from "../components/Modal";
import SupplierForm from "../components/SupplierForm";
import ConfirmModal from "../components/ConfirmModal";
import { useApp } from "../contexts/AppContext";
import { supplierService } from "../services/firebaseService";
import { useRealtimeData, useCRUD } from "../hooks/useAuth.jsx";
import { STATUSES } from "../models";

const Suppliers = forwardRef((props, ref) => {
  const { state, dispatch, actions, showNotification, logAction } = useApp();
  const [searchTerm, setSearchTerm] = useState("");
  const [filterType, setFilterType] = useState("all");
  const [filterStatus, setFilterStatus] = useState("all");
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [selectedSupplier, setSelectedSupplier] = useState(null);

  // Utiliser les hooks Firebase
  const {
    data: suppliers,
    loading: suppliersLoading,
    error: suppliersError,
  } = useRealtimeData(supplierService);
  const {
    loading: crudLoading,
    create,
    update,
    remove,
  } = useCRUD(supplierService);

  // Expose handleAddClick method to parent component
  useImperativeHandle(ref, () => ({
    handleAddClick: () => setShowAddModal(true),
  }));

  // Gérer les erreurs de chargement
  useEffect(() => {
    if (suppliersError) {
      showNotification("Erreur lors du chargement des fournisseurs", "error");
    }
  }, [suppliersError, showNotification]);

  const handleAddSupplier = async (supplierData) => {
    try {
      const newSupplier = await create(supplierData);
      await logAction(
        "CREATE",
        "suppliers",
        "supplier",
        newSupplier.id,
        {},
        newSupplier
      );
      showNotification("Fournisseur ajouté avec succès", "success");
      setShowAddModal(false);
    } catch (error) {
      showNotification("Erreur lors de l'ajout du fournisseur", "error");
    }
  };

  const handleEditSupplier = async (supplierData) => {
    try {
      const updatedSupplier = await update(selectedSupplier.id, supplierData);
      await logAction(
        "UPDATE",
        "suppliers",
        "supplier",
        selectedSupplier.id,
        selectedSupplier,
        updatedSupplier
      );
      showNotification("Fournisseur modifié avec succès", "success");
      setShowEditModal(false);
      setSelectedSupplier(null);
    } catch (error) {
      showNotification(
        "Erreur lors de la modification du fournisseur",
        "error"
      );
    }
  };

  const handleDeleteSupplier = async () => {
    try {
      await remove(selectedSupplier.id);
      await logAction(
        "DELETE",
        "suppliers",
        "supplier",
        selectedSupplier.id,
        selectedSupplier,
        {}
      );
      showNotification("Fournisseur supprimé avec succès", "success");
      setShowDeleteModal(false);
      setSelectedSupplier(null);
    } catch (error) {
      showNotification("Erreur lors de la suppression du fournisseur", "error");
    }
  };

  // Filtrage des fournisseurs
  const filteredSuppliers = suppliers.filter((supplier) => {
    const matchesSearch =
      supplier.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      supplier.country.toLowerCase().includes(searchTerm.toLowerCase()) ||
      supplier.contactPerson.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesType = filterType === "all" || supplier.type === filterType;
    const matchesStatus =
      filterStatus === "all" || supplier.status === filterStatus;

    return matchesSearch && matchesType && matchesStatus;
  });

  const getStatusBadge = (status) => {
    const statusConfig = {
      [STATUSES.ACTIVE]: {
        color: "bg-green-100 text-green-800",
        text: "Actif",
      },
      [STATUSES.INACTIVE]: {
        color: "bg-gray-100 text-gray-800",
        text: "Inactif",
      },
      [STATUSES.SUSPENDED]: {
        color: "bg-red-100 text-red-800",
        text: "Suspendu",
      },
      [STATUSES.PENDING]: {
        color: "bg-yellow-100 text-yellow-800",
        text: "En attente",
      },
    };

    const config = statusConfig[status] || statusConfig[STATUSES.INACTIVE];
    return (
      <span
        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}
      >
        {config.text}
      </span>
    );
  };

  const getTypeBadge = (type) => {
    const typeConfig = {
      manufacturer: { color: "bg-blue-100 text-blue-800", text: "Fabricant" },
      distributor: {
        color: "bg-purple-100 text-purple-800",
        text: "Distributeur",
      },
      importer: { color: "bg-orange-100 text-orange-800", text: "Importateur" },
    };

    const config = typeConfig[type] || typeConfig.distributor;
    return (
      <span
        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}
      >
        {config.text}
      </span>
    );
  };

  const getRatingStars = (rating) => {
    return (
      <div className="flex items-center">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`h-4 w-4 ${
              star <= rating ? "text-yellow-400 fill-current" : "text-gray-300"
            }`}
          />
        ))}
        <span className="ml-1 text-sm text-gray-600">({rating})</span>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">
            Fournisseurs & Importateurs
          </h1>
          <p className="text-gray-600">Gérez vos partenaires commerciaux</p>
        </div>
        <div className="flex space-x-3">
          <button className="btn-secondary">
            <Download className="h-4 w-4 mr-2" />
            Exporter
          </button>
          <button onClick={() => setShowAddModal(true)} className="btn-primary">
            <Plus className="h-4 w-4 mr-2" />
            Ajouter Fournisseur
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                type="text"
                placeholder="Rechercher par nom, pays ou contact..."
                className="input pl-10"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </div>
          <div className="flex gap-3">
            <select
              className="input min-w-[140px]"
              value={filterType}
              onChange={(e) => setFilterType(e.target.value)}
            >
              <option value="all">Tous types</option>
              <option value="manufacturer">Fabricants</option>
              <option value="distributor">Distributeurs</option>
              <option value="importer">Importateurs</option>
            </select>
            <select
              className="input min-w-[140px]"
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
            >
              <option value="all">Tous statuts</option>
              <option value="active">Actifs</option>
              <option value="inactive">Inactifs</option>
              <option value="suspended">Suspendus</option>
              <option value="pending">En attente</option>
            </select>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Building className="h-6 w-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">
                Total Fournisseurs
              </p>
              <p className="text-2xl font-bold text-gray-900">
                {suppliers.length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <Award className="h-6 w-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Actifs</p>
              <p className="text-2xl font-bold text-gray-900">
                {suppliers.filter((s) => s.status === STATUSES.ACTIVE).length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center">
            <div className="p-2 bg-purple-100 rounded-lg">
              <Globe className="h-6 w-6 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Pays</p>
              <p className="text-2xl font-bold text-gray-900">
                {new Set(suppliers.map((s) => s.country)).size}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center">
            <div className="p-2 bg-orange-100 rounded-lg">
              <Star className="h-6 w-6 text-orange-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Note Moyenne</p>
              <p className="text-2xl font-bold text-gray-900">
                {suppliers.length > 0
                  ? (
                      suppliers.reduce((acc, s) => acc + (s.rating || 0), 0) /
                      suppliers.length
                    ).toFixed(1)
                  : "0.0"}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Suppliers Table */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Fournisseur
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Type & Pays
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Contact
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Évaluation
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Contrats
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Statut
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredSuppliers.map((supplier) => (
                <tr key={supplier.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10">
                        <div className="h-10 w-10 rounded-lg bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
                          <Building className="h-5 w-5 text-white" />
                        </div>
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">
                          {supplier.name}
                        </div>
                        <div className="text-sm text-gray-500">
                          {supplier.licenseNumber}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="space-y-1">
                      {getTypeBadge(supplier.type)}
                      <div className="flex items-center text-sm text-gray-500">
                        <MapPin className="h-3 w-3 mr-1" />
                        {supplier.country}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      {supplier.contactPerson}
                    </div>
                    <div className="flex items-center text-sm text-gray-500">
                      <Phone className="h-3 w-3 mr-1" />
                      {supplier.phone}
                    </div>
                    <div className="flex items-center text-sm text-gray-500">
                      <Mail className="h-3 w-3 mr-1" />
                      {supplier.email}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {getRatingStars(supplier.rating || 0)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      {supplier.totalContracts || 0}
                    </div>
                    <div className="text-sm text-gray-500">contrats actifs</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {getStatusBadge(supplier.status)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2">
                      <button
                        onClick={() => {
                          setSelectedSupplier(supplier);
                          setShowViewModal(true);
                        }}
                        className="text-blue-600 hover:text-blue-900"
                        title="Voir détails"
                      >
                        <Eye className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => {
                          setSelectedSupplier(supplier);
                          setShowEditModal(true);
                        }}
                        className="text-indigo-600 hover:text-indigo-900"
                        title="Modifier"
                      >
                        <Edit className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => {
                          setSelectedSupplier(supplier);
                          setShowDeleteModal(true);
                        }}
                        className="text-red-600 hover:text-red-900"
                        title="Supprimer"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {filteredSuppliers.length === 0 && (
          <div className="text-center py-12">
            <Building className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">
              Aucun fournisseur
            </h3>
            <p className="mt-1 text-sm text-gray-500">
              {searchTerm || filterType !== "all" || filterStatus !== "all"
                ? "Aucun fournisseur ne correspond aux critères de recherche."
                : "Commencez par ajouter un nouveau fournisseur."}
            </p>
          </div>
        )}
      </div>

      {/* Modals */}
      <Modal
        isOpen={showAddModal}
        onClose={() => setShowAddModal(false)}
        title="Ajouter un Fournisseur"
        size="lg"
      >
        <SupplierForm
          onSubmit={handleAddSupplier}
          onCancel={() => setShowAddModal(false)}
          isLoading={crudLoading}
        />
      </Modal>

      <Modal
        isOpen={showEditModal}
        onClose={() => setShowEditModal(false)}
        title="Modifier le Fournisseur"
        size="lg"
      >
        <SupplierForm
          supplier={selectedSupplier}
          onSubmit={handleEditSupplier}
          onCancel={() => setShowEditModal(false)}
          isLoading={crudLoading}
        />
      </Modal>

      <ConfirmModal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        onConfirm={handleDeleteSupplier}
        title="Supprimer le Fournisseur"
        message={`Êtes-vous sûr de vouloir supprimer le fournisseur "${selectedSupplier?.name}" ? Cette action est irréversible.`}
        isLoading={crudLoading}
      />
    </div>
  );
});

Suppliers.displayName = "Suppliers";

export default Suppliers;
