import React, { useState, forwardRef, useImperativeHandle } from "react";
import {
  Plus,
  Search,
  MapPin,
  Phone,
  Mail,
  Edit,
  Trash2,
  Hospital,
  Users,
  Bed,
  Eye,
  Download,
} from "lucide-react";
import Modal from "../components/Modal";
import HospitalForm from "../components/HospitalForm";
import ConfirmModal from "../components/ConfirmModal";

const Hospitals = forwardRef((props, ref) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [selectedHospital, setSelectedHospital] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [hospitals, setHospitals] = useState([
    {
      id: 1,
      name: "City General Hospital",
      address: "100 Medical Center Drive, Downtown",
      phone: "+****************",
      email: "<EMAIL>",
      administrator: "Dr. Robert Wilson",
      licenseNumber: "HOS2024001",
      status: "active",
      totalBeds: 250,
      occupiedBeds: 180,
      departments: ["Emergency", "Surgery", "Cardiology", "Pediatrics"],
      totalMedicines: 892,
    },
    {
      id: 2,
      name: "St. Mary's Medical Center",
      address: "200 Healthcare Boulevard, Midtown",
      phone: "+****************",
      email: "<EMAIL>",
      administrator: "Dr. Lisa Thompson",
      licenseNumber: "HOS2024002",
      status: "active",
      totalBeds: 180,
      occupiedBeds: 145,
      departments: ["Maternity", "Oncology", "Neurology"],
      totalMedicines: 654,
    },
  ]);

  // Expose handleAddClick method to parent component
  useImperativeHandle(ref, () => ({
    handleAddClick: () => setShowAddModal(true),
  }));

  // CRUD Operations
  const handleAddHospital = async (hospitalData) => {
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));

      const newHospital = {
        id: hospitals.length + 1,
        ...hospitalData,
        status: "active",
        occupiedBeds: 0,
        totalMedicines: 0,
      };

      setHospitals([...hospitals, newHospital]);
      setShowAddModal(false);
    } catch (error) {
      console.error("Error adding hospital:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleEditHospital = async (hospitalData) => {
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));

      const updatedHospital = {
        ...selectedHospital,
        ...hospitalData,
      };

      setHospitals(
        hospitals.map((hospital) =>
          hospital.id === selectedHospital.id ? updatedHospital : hospital
        )
      );
      setShowEditModal(false);
      setSelectedHospital(null);
    } catch (error) {
      console.error("Error updating hospital:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteHospital = async () => {
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));

      setHospitals(
        hospitals.filter((hospital) => hospital.id !== selectedHospital.id)
      );
      setShowDeleteModal(false);
      setSelectedHospital(null);
    } catch (error) {
      console.error("Error deleting hospital:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const openEditModal = (hospital) => {
    setSelectedHospital(hospital);
    setShowEditModal(true);
  };

  const openDeleteModal = (hospital) => {
    setSelectedHospital(hospital);
    setShowDeleteModal(true);
  };

  const openViewModal = (hospital) => {
    setSelectedHospital(hospital);
    setShowViewModal(true);
  };

  const getStatusBadge = (status) => {
    return status === "active" ? (
      <span className="status-active">Active</span>
    ) : (
      <span className="status-inactive">Inactive</span>
    );
  };

  const getOccupancyRate = (occupied, total) => {
    const rate = (occupied / total) * 100;
    return {
      rate: rate.toFixed(1),
      color:
        rate > 90
          ? "text-danger-600"
          : rate > 75
          ? "text-warning-600"
          : "text-success-600",
    };
  };

  const filteredHospitals = hospitals.filter(
    (hospital) =>
      hospital.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      hospital.address.toLowerCase().includes(searchTerm.toLowerCase()) ||
      hospital.administrator.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Hospitals</h1>
          <p className="text-gray-600">
            Manage partner hospitals and medical centers
          </p>
        </div>
        <div className="flex space-x-3">
          <button className="btn-secondary">
            <Download className="h-4 w-4 mr-2" />
            Export
          </button>
          <button onClick={() => setShowAddModal(true)} className="btn-primary">
            <Plus className="h-4 w-4 mr-2" />
            Add Hospital
          </button>
        </div>
      </div>

      {/* Search */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
        <input
          type="text"
          placeholder="Search hospitals..."
          className="input pl-10"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
      </div>

      {/* Hospitals Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {filteredHospitals.map((hospital) => {
          const occupancy = getOccupancyRate(
            hospital.occupiedBeds,
            hospital.totalBeds
          );
          return (
            <div key={hospital.id} className="card">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="flex-shrink-0">
                    <Hospital className="h-8 w-8 text-primary-600" />
                  </div>
                  <div>
                    <h3 className="text-lg font-medium text-gray-900">
                      {hospital.name}
                    </h3>
                    <p className="text-sm text-gray-500">
                      License: {hospital.licenseNumber}
                    </p>
                  </div>
                </div>
                {getStatusBadge(hospital.status)}
              </div>

              <div className="space-y-3">
                <div className="flex items-start space-x-2">
                  <MapPin className="h-4 w-4 text-gray-400 mt-0.5" />
                  <p className="text-sm text-gray-600">{hospital.address}</p>
                </div>

                <div className="flex items-center space-x-2">
                  <Phone className="h-4 w-4 text-gray-400" />
                  <p className="text-sm text-gray-600">{hospital.phone}</p>
                </div>

                <div className="flex items-center space-x-2">
                  <Mail className="h-4 w-4 text-gray-400" />
                  <p className="text-sm text-gray-600">{hospital.email}</p>
                </div>

                <div className="flex items-center space-x-2">
                  <Users className="h-4 w-4 text-gray-400" />
                  <p className="text-sm text-gray-600">
                    Administrator: {hospital.administrator}
                  </p>
                </div>
              </div>

              {/* Departments */}
              {hospital.departments.length > 0 && (
                <div className="mt-4">
                  <p className="text-sm font-medium text-gray-900 mb-2">
                    Departments
                  </p>
                  <div className="flex flex-wrap gap-1">
                    {hospital.departments.map((dept, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-primary-100 text-primary-800"
                      >
                        {dept}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* Stats */}
              <div className="mt-4 pt-4 border-t border-gray-200">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <div className="flex items-center space-x-1">
                      <Bed className="h-4 w-4 text-gray-400" />
                      <p className="text-gray-500">Bed Occupancy</p>
                    </div>
                    <p className="font-medium text-gray-900">
                      {hospital.occupiedBeds}/{hospital.totalBeds}
                    </p>
                    <p className={`text-xs ${occupancy.color}`}>
                      {occupancy.rate}% occupied
                    </p>
                  </div>
                  <div>
                    <p className="text-gray-500">Medicines</p>
                    <p className="font-medium text-gray-900">
                      {hospital.totalMedicines}
                    </p>
                  </div>
                </div>
              </div>

              {/* Actions */}
              <div className="mt-4 flex justify-end space-x-2">
                <button
                  onClick={() => openViewModal(hospital)}
                  className="p-2 text-blue-600 hover:text-blue-900 hover:bg-blue-50 rounded-lg transition-colors"
                  title="View Details"
                >
                  <Eye className="h-4 w-4" />
                </button>
                <button
                  onClick={() => openEditModal(hospital)}
                  className="p-2 text-green-600 hover:text-green-900 hover:bg-green-50 rounded-lg transition-colors"
                  title="Edit Hospital"
                >
                  <Edit className="h-4 w-4" />
                </button>
                <button
                  onClick={() => openDeleteModal(hospital)}
                  className="p-2 text-red-600 hover:text-red-900 hover:bg-red-50 rounded-lg transition-colors"
                  title="Delete Hospital"
                >
                  <Trash2 className="h-4 w-4" />
                </button>
              </div>
            </div>
          );
        })}
      </div>

      {filteredHospitals.length === 0 && (
        <div className="text-center py-12">
          <Hospital className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">
            No hospitals found
          </h3>
          <p className="mt-1 text-sm text-gray-500">
            {searchTerm
              ? "Try adjusting your search terms."
              : "Get started by adding a new hospital."}
          </p>
        </div>
      )}

      {/* Modals */}
      {/* Add Hospital Modal */}
      <Modal
        isOpen={showAddModal}
        onClose={() => setShowAddModal(false)}
        title="Add New Hospital"
        size="lg"
      >
        <HospitalForm
          onSubmit={handleAddHospital}
          onCancel={() => setShowAddModal(false)}
          isLoading={isLoading}
        />
      </Modal>

      {/* Edit Hospital Modal */}
      <Modal
        isOpen={showEditModal}
        onClose={() => setShowEditModal(false)}
        title="Edit Hospital"
        size="lg"
      >
        <HospitalForm
          hospital={selectedHospital}
          onSubmit={handleEditHospital}
          onCancel={() => setShowEditModal(false)}
          isLoading={isLoading}
        />
      </Modal>

      {/* View Hospital Modal */}
      <Modal
        isOpen={showViewModal}
        onClose={() => setShowViewModal(false)}
        title="Hospital Details"
        size="md"
      >
        {selectedHospital && (
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="label">Hospital Name</label>
                <p className="text-gray-900 font-medium">
                  {selectedHospital.name}
                </p>
              </div>
              <div>
                <label className="label">Administrator</label>
                <p className="text-gray-900">
                  {selectedHospital.administrator}
                </p>
              </div>
              <div className="md:col-span-2">
                <label className="label">Address</label>
                <p className="text-gray-900">{selectedHospital.address}</p>
              </div>
              <div>
                <label className="label">Phone</label>
                <p className="text-gray-900">{selectedHospital.phone}</p>
              </div>
              <div>
                <label className="label">Email</label>
                <p className="text-gray-900">{selectedHospital.email}</p>
              </div>
              <div>
                <label className="label">License Number</label>
                <p className="text-gray-900">
                  {selectedHospital.licenseNumber}
                </p>
              </div>
              <div>
                <label className="label">Total Beds</label>
                <p className="text-gray-900">{selectedHospital.totalBeds}</p>
              </div>
              <div>
                <label className="label">Occupied Beds</label>
                <p className="text-gray-900">{selectedHospital.occupiedBeds}</p>
              </div>
              <div>
                <label className="label">Total Medicines</label>
                <p className="text-gray-900 font-medium">
                  {selectedHospital.totalMedicines}
                </p>
              </div>
              <div>
                <label className="label">Status</label>
                <div>{getStatusBadge(selectedHospital.status)}</div>
              </div>
            </div>
            {selectedHospital.departments &&
              selectedHospital.departments.length > 0 && (
                <div>
                  <label className="label">Departments</label>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {selectedHospital.departments.map((dept, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-primary-100 text-primary-800"
                      >
                        {dept}
                      </span>
                    ))}
                  </div>
                </div>
              )}
            <div className="pt-4 border-t border-gray-200">
              <button
                onClick={() => setShowViewModal(false)}
                className="btn-secondary w-full"
              >
                Close
              </button>
            </div>
          </div>
        )}
      </Modal>

      {/* Delete Confirmation Modal */}
      <ConfirmModal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        onConfirm={handleDeleteHospital}
        title="Delete Hospital"
        message="Are you sure you want to delete this hospital? This action cannot be undone."
        confirmText="Delete"
        cancelText="Cancel"
        type="danger"
        isLoading={isLoading}
        item={selectedHospital?.name}
      />
    </div>
  );
});

Hospitals.displayName = "Hospitals";

export default Hospitals;
