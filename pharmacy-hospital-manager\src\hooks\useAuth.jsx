// Hook personnalisé pour l'authentification Firebase
import { useState, useEffect, createContext, useContext } from 'react';
import { onAuthStateChanged } from 'firebase/auth';
import { doc, getDoc } from 'firebase/firestore';
import { auth, db, COLLECTIONS } from '../config/firebase';
import { authService } from '../services/firebaseService';

// Contexte d'authentification
const AuthContext = createContext();

// Provider d'authentification
export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
      try {
        if (firebaseUser) {
          // Récupérer les données utilisateur depuis Firestore
          const userDocRef = doc(db, COLLECTIONS.USERS, firebaseUser.uid);
          const userDoc = await getDoc(userDocRef);
          
          if (userDoc.exists()) {
            const userData = {
              uid: firebaseUser.uid,
              email: firebaseUser.email,
              emailVerified: firebaseUser.emailVerified,
              ...userDoc.data()
            };
            setUser(userData);
          } else {
            // Créer un profil utilisateur par défaut si il n'existe pas
            const defaultUserData = {
              uid: firebaseUser.uid,
              email: firebaseUser.email,
              name: firebaseUser.displayName || 'Utilisateur',
              role: 'viewer',
              permissions: ['read'],
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString()
            };
            setUser(defaultUserData);
          }
        } else {
          setUser(null);
        }
      } catch (error) {
        console.error('Erreur lors de la récupération des données utilisateur:', error);
        setError(error.message);
      } finally {
        setLoading(false);
      }
    });

    return () => unsubscribe();
  }, []);

  // Fonction de connexion
  const signIn = async (email, password) => {
    try {
      setLoading(true);
      setError(null);
      const userData = await authService.signIn(email, password);
      setUser(userData);
      return userData;
    } catch (error) {
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Fonction d'inscription
  const signUp = async (email, password, userData) => {
    try {
      setLoading(true);
      setError(null);
      const newUser = await authService.signUp(email, password, userData);
      setUser(newUser);
      return newUser;
    } catch (error) {
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Fonction de déconnexion
  const signOut = async () => {
    try {
      setLoading(true);
      await authService.signOut();
      setUser(null);
    } catch (error) {
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Fonction de réinitialisation du mot de passe
  const resetPassword = async (email) => {
    try {
      setError(null);
      await authService.resetPassword(email);
    } catch (error) {
      setError(error.message);
      throw error;
    }
  };

  // Vérifier les permissions
  const hasPermission = (module, action) => {
    if (!user) return false;
    
    // Les admins ont toutes les permissions
    if (user.role === 'admin') return true;
    
    // Vérifier les permissions spécifiques
    const userPermissions = user.permissions || [];
    return userPermissions.includes(`${module}:${action}`) || userPermissions.includes('all');
  };

  // Vérifier le rôle
  const hasRole = (role) => {
    if (!user) return false;
    return user.role === role;
  };

  // Vérifier si l'utilisateur est connecté
  const isAuthenticated = () => {
    return !!user;
  };

  const value = {
    user,
    loading,
    error,
    signIn,
    signUp,
    signOut,
    resetPassword,
    hasPermission,
    hasRole,
    isAuthenticated,
    setError
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// Hook pour utiliser le contexte d'authentification
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth doit être utilisé dans un AuthProvider');
  }
  return context;
};

// Hook pour les données en temps réel
export const useRealtimeData = (service, options = {}) => {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    let unsubscribe;

    const setupListener = async () => {
      try {
        setLoading(true);
        setError(null);

        unsubscribe = service.onSnapshot((documents) => {
          setData(documents);
          setLoading(false);
        }, options);
      } catch (error) {
        console.error('Erreur lors de la configuration de l\'écoute en temps réel:', error);
        setError(error.message);
        setLoading(false);
      }
    };

    setupListener();

    return () => {
      if (unsubscribe) {
        unsubscribe();
      }
    };
  }, [service, JSON.stringify(options)]);

  return { data, loading, error };
};

// Hook pour les opérations CRUD
export const useCRUD = (service) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const create = async (data) => {
    try {
      setLoading(true);
      setError(null);
      const result = await service.create(data);
      return result;
    } catch (error) {
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const update = async (id, data) => {
    try {
      setLoading(true);
      setError(null);
      const result = await service.update(id, data);
      return result;
    } catch (error) {
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const remove = async (id) => {
    try {
      setLoading(true);
      setError(null);
      const result = await service.delete(id);
      return result;
    } catch (error) {
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const getById = async (id) => {
    try {
      setLoading(true);
      setError(null);
      const result = await service.getById(id);
      return result;
    } catch (error) {
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const getAll = async (options = {}) => {
    try {
      setLoading(true);
      setError(null);
      const result = await service.getAll(options);
      return result;
    } catch (error) {
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const search = async (searchTerm, searchFields) => {
    try {
      setLoading(true);
      setError(null);
      const result = await service.search(searchTerm, searchFields);
      return result;
    } catch (error) {
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  return {
    loading,
    error,
    create,
    update,
    remove,
    getById,
    getAll,
    search,
    setError
  };
};

// Hook pour la pagination
export const usePagination = (service, options = {}) => {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [hasMore, setHasMore] = useState(true);
  const [lastDoc, setLastDoc] = useState(null);

  const loadMore = async () => {
    try {
      setLoading(true);
      setError(null);

      const result = await service.getAll({
        ...options,
        startAfterDoc: lastDoc
      });

      setData(prevData => [...prevData, ...result.data]);
      setLastDoc(result.lastDoc);
      setHasMore(result.hasMore);
    } catch (error) {
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const reset = () => {
    setData([]);
    setLastDoc(null);
    setHasMore(true);
    setError(null);
  };

  useEffect(() => {
    loadMore();
  }, []);

  return {
    data,
    loading,
    error,
    hasMore,
    loadMore,
    reset
  };
};

export default useAuth;
