import React, { useState, forwardRef, useImperativeHandle } from "react";
import {
  Plus,
  Search,
  Filter,
  Edit,
  Trash2,
  Calendar,
  Package,
  AlertTriangle,
  Eye,
  Download,
} from "lucide-react";
import Modal from "../components/Modal";
import MedicineForm from "../components/MedicineForm";
import ConfirmModal from "../components/ConfirmModal";

const Medicines = forwardRef((props, ref) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [selectedMedicine, setSelectedMedicine] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [medicines, setMedicines] = useState([
    {
      id: 1,
      name: "Paracetamol",
      dosage: "500mg",
      form: "Tablet",
      manufacturer: "PharmaCorp",
      batchNumber: "*********",
      expiryDate: "2025-12-31",
      quantity: 450,
      minQuantity: 100,
      price: 0.25,
      status: "active",
    },
    {
      id: 2,
      name: "Amoxicillin",
      dosage: "250mg",
      form: "Capsule",
      manufacturer: "MediLab",
      batchNumber: "*********",
      expiryDate: "2025-08-15",
      quantity: 78,
      minQuantity: 150,
      price: 0.45,
      status: "low_stock",
    },
    {
      id: 3,
      name: "Ibuprofen",
      dosage: "400mg",
      form: "Tablet",
      manufacturer: "HealthPlus",
      batchNumber: "*********",
      expiryDate: "2026-03-20",
      quantity: 23,
      minQuantity: 80,
      price: 0.35,
      status: "critical",
    },
  ]);

  // Expose handleAddClick method to parent component
  useImperativeHandle(ref, () => ({
    handleAddClick: () => setShowAddModal(true),
  }));

  // CRUD Operations
  const handleAddMedicine = async (medicineData) => {
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));

      const newMedicine = {
        id: medicines.length + 1,
        ...medicineData,
        status:
          medicineData.quantity <= medicineData.minQuantity
            ? "low_stock"
            : "active",
      };

      setMedicines([...medicines, newMedicine]);
      setShowAddModal(false);
    } catch (error) {
      console.error("Error adding medicine:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleEditMedicine = async (medicineData) => {
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));

      const updatedMedicine = {
        ...selectedMedicine,
        ...medicineData,
        status:
          medicineData.quantity <= medicineData.minQuantity
            ? "low_stock"
            : "active",
      };

      setMedicines(
        medicines.map((med) =>
          med.id === selectedMedicine.id ? updatedMedicine : med
        )
      );
      setShowEditModal(false);
      setSelectedMedicine(null);
    } catch (error) {
      console.error("Error updating medicine:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteMedicine = async () => {
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));

      setMedicines(medicines.filter((med) => med.id !== selectedMedicine.id));
      setShowDeleteModal(false);
      setSelectedMedicine(null);
    } catch (error) {
      console.error("Error deleting medicine:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const openEditModal = (medicine) => {
    setSelectedMedicine(medicine);
    setShowEditModal(true);
  };

  const openDeleteModal = (medicine) => {
    setSelectedMedicine(medicine);
    setShowDeleteModal(true);
  };

  const openViewModal = (medicine) => {
    setSelectedMedicine(medicine);
    setShowViewModal(true);
  };

  const getStatusBadge = (status) => {
    switch (status) {
      case "active":
        return <span className="status-active">Active</span>;
      case "low_stock":
        return <span className="status-warning">Low Stock</span>;
      case "critical":
        return <span className="status-danger">Critical</span>;
      default:
        return <span className="status-inactive">Inactive</span>;
    }
  };

  const filteredMedicines = medicines.filter(
    (medicine) =>
      medicine.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      medicine.manufacturer.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Medicines</h1>
          <p className="text-gray-600">Manage your medicine inventory</p>
        </div>
        <div className="flex space-x-3">
          <button className="btn-secondary">
            <Download className="h-4 w-4 mr-2" />
            Export
          </button>
          <button onClick={() => setShowAddModal(true)} className="btn-primary">
            <Plus className="h-4 w-4 mr-2" />
            Add Medicine
          </button>
        </div>
      </div>

      {/* Search and Filter */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <input
            type="text"
            placeholder="Search medicines..."
            className="input pl-10"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <button className="btn-secondary">
          <Filter className="h-4 w-4 mr-2" />
          Filter
        </button>
      </div>

      {/* Medicines Table */}
      <div className="card overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Medicine
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Details
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Stock
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredMedicines.map((medicine) => (
                <tr key={medicine.id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {medicine.name} {medicine.dosage}
                      </div>
                      <div className="text-sm text-gray-500">
                        {medicine.form}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      {medicine.manufacturer}
                    </div>
                    <div className="text-sm text-gray-500">
                      Batch: {medicine.batchNumber}
                    </div>
                    <div className="text-sm text-gray-500 flex items-center">
                      <Calendar className="h-3 w-3 mr-1" />
                      Exp: {medicine.expiryDate}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900 flex items-center">
                      <Package className="h-4 w-4 mr-1" />
                      {medicine.quantity}
                    </div>
                    <div className="text-sm text-gray-500">
                      Min: {medicine.minQuantity}
                    </div>
                    <div className="text-sm text-gray-500">
                      ${medicine.price}/unit
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {getStatusBadge(medicine.status)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2">
                      <button
                        onClick={() => openViewModal(medicine)}
                        className="p-2 text-blue-600 hover:text-blue-900 hover:bg-blue-50 rounded-lg transition-colors"
                        title="View Details"
                      >
                        <Eye className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => openEditModal(medicine)}
                        className="p-2 text-green-600 hover:text-green-900 hover:bg-green-50 rounded-lg transition-colors"
                        title="Edit Medicine"
                      >
                        <Edit className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => openDeleteModal(medicine)}
                        className="p-2 text-red-600 hover:text-red-900 hover:bg-red-50 rounded-lg transition-colors"
                        title="Delete Medicine"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Modals */}
      {/* Add Medicine Modal */}
      <Modal
        isOpen={showAddModal}
        onClose={() => setShowAddModal(false)}
        title="Add New Medicine"
        size="lg"
      >
        <MedicineForm
          onSubmit={handleAddMedicine}
          onCancel={() => setShowAddModal(false)}
          isLoading={isLoading}
        />
      </Modal>

      {/* Edit Medicine Modal */}
      <Modal
        isOpen={showEditModal}
        onClose={() => setShowEditModal(false)}
        title="Edit Medicine"
        size="lg"
      >
        <MedicineForm
          medicine={selectedMedicine}
          onSubmit={handleEditMedicine}
          onCancel={() => setShowEditModal(false)}
          isLoading={isLoading}
        />
      </Modal>

      {/* View Medicine Modal */}
      <Modal
        isOpen={showViewModal}
        onClose={() => setShowViewModal(false)}
        title="Medicine Details"
        size="md"
      >
        {selectedMedicine && (
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="label">Medicine Name</label>
                <p className="text-gray-900 font-medium">
                  {selectedMedicine.name}
                </p>
              </div>
              <div>
                <label className="label">Dosage</label>
                <p className="text-gray-900">{selectedMedicine.dosage}</p>
              </div>
              <div>
                <label className="label">Form</label>
                <p className="text-gray-900">{selectedMedicine.form}</p>
              </div>
              <div>
                <label className="label">Manufacturer</label>
                <p className="text-gray-900">{selectedMedicine.manufacturer}</p>
              </div>
              <div>
                <label className="label">Batch Number</label>
                <p className="text-gray-900">{selectedMedicine.batchNumber}</p>
              </div>
              <div>
                <label className="label">Expiry Date</label>
                <p className="text-gray-900">{selectedMedicine.expiryDate}</p>
              </div>
              <div>
                <label className="label">Current Stock</label>
                <p className="text-gray-900 font-medium">
                  {selectedMedicine.quantity} units
                </p>
              </div>
              <div>
                <label className="label">Minimum Stock</label>
                <p className="text-gray-900">
                  {selectedMedicine.minQuantity} units
                </p>
              </div>
              <div>
                <label className="label">Price per Unit</label>
                <p className="text-gray-900">${selectedMedicine.price}</p>
              </div>
              <div>
                <label className="label">Status</label>
                <div>{getStatusBadge(selectedMedicine.status)}</div>
              </div>
            </div>
            <div className="pt-4 border-t border-gray-200">
              <button
                onClick={() => setShowViewModal(false)}
                className="btn-secondary w-full"
              >
                Close
              </button>
            </div>
          </div>
        )}
      </Modal>

      {/* Delete Confirmation Modal */}
      <ConfirmModal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        onConfirm={handleDeleteMedicine}
        title="Delete Medicine"
        message="Are you sure you want to delete this medicine? This action cannot be undone."
        confirmText="Delete"
        cancelText="Cancel"
        type="danger"
        isLoading={isLoading}
        item={selectedMedicine?.name}
      />
    </div>
  );
});

Medicines.displayName = "Medicines";

export default Medicines;
