import React, { useState, forwardRef, useImperativeHandle } from "react";
import {
  Plus,
  Search,
  MapPin,
  Phone,
  Mail,
  Edit,
  Trash2,
  Building2,
  Clock,
  Users,
  Eye,
  Download,
} from "lucide-react";
import Modal from "../components/Modal";
import PharmacyForm from "../components/PharmacyForm";
import ConfirmModal from "../components/ConfirmModal";

const Pharmacies = forwardRef((props, ref) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [selectedPharmacy, setSelectedPharmacy] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [pharmacies, setPharmacies] = useState([
    {
      id: 1,
      name: "Central Pharmacy",
      address: "123 Main Street, Downtown",
      phone: "+****************",
      email: "<EMAIL>",
      manager: "Dr. Sarah Johnson",
      licenseNumber: "*********",
      status: "active",
      operatingHours: "8:00 AM - 10:00 PM",
      totalMedicines: 1247,
      lowStockItems: 12,
    },
    {
      id: 2,
      name: "Westside Pharmacy",
      address: "456 Oak Avenue, Westside",
      phone: "+****************",
      email: "<EMAIL>",
      manager: "Dr. Michael Chen",
      licenseNumber: "*********",
      status: "active",
      operatingHours: "9:00 AM - 9:00 PM",
      totalMedicines: 892,
      lowStockItems: 8,
    },
    {
      id: 3,
      name: "Community Care Pharmacy",
      address: "789 Pine Road, Suburbs",
      phone: "+****************",
      email: "<EMAIL>",
      manager: "Dr. Emily Rodriguez",
      licenseNumber: "*********",
      status: "inactive",
      operatingHours: "10:00 AM - 8:00 PM",
      totalMedicines: 654,
      lowStockItems: 23,
    },
  ]);

  // Expose handleAddClick method to parent component
  useImperativeHandle(ref, () => ({
    handleAddClick: () => setShowAddModal(true),
  }));

  // CRUD Operations
  const handleAddPharmacy = async (pharmacyData) => {
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));

      const newPharmacy = {
        id: pharmacies.length + 1,
        ...pharmacyData,
        status: "active",
        totalMedicines: 0,
        lowStockItems: 0,
      };

      setPharmacies([...pharmacies, newPharmacy]);
      setShowAddModal(false);
    } catch (error) {
      console.error("Error adding pharmacy:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleEditPharmacy = async (pharmacyData) => {
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));

      const updatedPharmacy = {
        ...selectedPharmacy,
        ...pharmacyData,
      };

      setPharmacies(
        pharmacies.map((pharmacy) =>
          pharmacy.id === selectedPharmacy.id ? updatedPharmacy : pharmacy
        )
      );
      setShowEditModal(false);
      setSelectedPharmacy(null);
    } catch (error) {
      console.error("Error updating pharmacy:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeletePharmacy = async () => {
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));

      setPharmacies(
        pharmacies.filter((pharmacy) => pharmacy.id !== selectedPharmacy.id)
      );
      setShowDeleteModal(false);
      setSelectedPharmacy(null);
    } catch (error) {
      console.error("Error deleting pharmacy:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const openEditModal = (pharmacy) => {
    setSelectedPharmacy(pharmacy);
    setShowEditModal(true);
  };

  const openDeleteModal = (pharmacy) => {
    setSelectedPharmacy(pharmacy);
    setShowDeleteModal(true);
  };

  const openViewModal = (pharmacy) => {
    setSelectedPharmacy(pharmacy);
    setShowViewModal(true);
  };

  const getStatusBadge = (status) => {
    return status === "active" ? (
      <span className="status-active">Active</span>
    ) : (
      <span className="status-inactive">Inactive</span>
    );
  };

  const filteredPharmacies = pharmacies.filter(
    (pharmacy) =>
      pharmacy.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      pharmacy.address.toLowerCase().includes(searchTerm.toLowerCase()) ||
      pharmacy.manager.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Pharmacies</h1>
          <p className="text-gray-600">
            Manage pharmacy locations and information
          </p>
        </div>
        <div className="flex space-x-3">
          <button className="btn-secondary">
            <Download className="h-4 w-4 mr-2" />
            Export
          </button>
          <button onClick={() => setShowAddModal(true)} className="btn-primary">
            <Plus className="h-4 w-4 mr-2" />
            Add Pharmacy
          </button>
        </div>
      </div>

      {/* Search */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
        <input
          type="text"
          placeholder="Search pharmacies..."
          className="input pl-10"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
      </div>

      {/* Pharmacies Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredPharmacies.map((pharmacy) => (
          <div key={pharmacy.id} className="card">
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center space-x-3">
                <div className="flex-shrink-0">
                  <Building2 className="h-8 w-8 text-primary-600" />
                </div>
                <div>
                  <h3 className="text-lg font-medium text-gray-900">
                    {pharmacy.name}
                  </h3>
                  <p className="text-sm text-gray-500">
                    License: {pharmacy.licenseNumber}
                  </p>
                </div>
              </div>
              {getStatusBadge(pharmacy.status)}
            </div>

            <div className="space-y-3">
              <div className="flex items-start space-x-2">
                <MapPin className="h-4 w-4 text-gray-400 mt-0.5" />
                <p className="text-sm text-gray-600">{pharmacy.address}</p>
              </div>

              <div className="flex items-center space-x-2">
                <Phone className="h-4 w-4 text-gray-400" />
                <p className="text-sm text-gray-600">{pharmacy.phone}</p>
              </div>

              <div className="flex items-center space-x-2">
                <Mail className="h-4 w-4 text-gray-400" />
                <p className="text-sm text-gray-600">{pharmacy.email}</p>
              </div>

              <div className="flex items-center space-x-2">
                <Users className="h-4 w-4 text-gray-400" />
                <p className="text-sm text-gray-600">
                  Manager: {pharmacy.manager}
                </p>
              </div>

              <div className="flex items-center space-x-2">
                <Clock className="h-4 w-4 text-gray-400" />
                <p className="text-sm text-gray-600">
                  {pharmacy.operatingHours}
                </p>
              </div>
            </div>

            {/* Stats */}
            <div className="mt-4 pt-4 border-t border-gray-200">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="text-gray-500">Total Medicines</p>
                  <p className="font-medium text-gray-900">
                    {pharmacy.totalMedicines}
                  </p>
                </div>
                <div>
                  <p className="text-gray-500">Low Stock Items</p>
                  <p
                    className={`font-medium ${
                      pharmacy.lowStockItems > 15
                        ? "text-danger-600"
                        : pharmacy.lowStockItems > 5
                        ? "text-warning-600"
                        : "text-success-600"
                    }`}
                  >
                    {pharmacy.lowStockItems}
                  </p>
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="mt-4 flex justify-end space-x-2">
              <button
                onClick={() => openViewModal(pharmacy)}
                className="p-2 text-blue-600 hover:text-blue-900 hover:bg-blue-50 rounded-lg transition-colors"
                title="View Details"
              >
                <Eye className="h-4 w-4" />
              </button>
              <button
                onClick={() => openEditModal(pharmacy)}
                className="p-2 text-green-600 hover:text-green-900 hover:bg-green-50 rounded-lg transition-colors"
                title="Edit Pharmacy"
              >
                <Edit className="h-4 w-4" />
              </button>
              <button
                onClick={() => openDeleteModal(pharmacy)}
                className="p-2 text-red-600 hover:text-red-900 hover:bg-red-50 rounded-lg transition-colors"
                title="Delete Pharmacy"
              >
                <Trash2 className="h-4 w-4" />
              </button>
            </div>
          </div>
        ))}
      </div>

      {filteredPharmacies.length === 0 && (
        <div className="text-center py-12">
          <Building2 className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">
            No pharmacies found
          </h3>
          <p className="mt-1 text-sm text-gray-500">
            {searchTerm
              ? "Try adjusting your search terms."
              : "Get started by adding a new pharmacy."}
          </p>
        </div>
      )}

      {/* Modals */}
      {/* Add Pharmacy Modal */}
      <Modal
        isOpen={showAddModal}
        onClose={() => setShowAddModal(false)}
        title="Add New Pharmacy"
        size="lg"
      >
        <PharmacyForm
          onSubmit={handleAddPharmacy}
          onCancel={() => setShowAddModal(false)}
          isLoading={isLoading}
        />
      </Modal>

      {/* Edit Pharmacy Modal */}
      <Modal
        isOpen={showEditModal}
        onClose={() => setShowEditModal(false)}
        title="Edit Pharmacy"
        size="lg"
      >
        <PharmacyForm
          pharmacy={selectedPharmacy}
          onSubmit={handleEditPharmacy}
          onCancel={() => setShowEditModal(false)}
          isLoading={isLoading}
        />
      </Modal>

      {/* View Pharmacy Modal */}
      <Modal
        isOpen={showViewModal}
        onClose={() => setShowViewModal(false)}
        title="Pharmacy Details"
        size="md"
      >
        {selectedPharmacy && (
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="label">Pharmacy Name</label>
                <p className="text-gray-900 font-medium">
                  {selectedPharmacy.name}
                </p>
              </div>
              <div>
                <label className="label">Manager</label>
                <p className="text-gray-900">{selectedPharmacy.manager}</p>
              </div>
              <div className="md:col-span-2">
                <label className="label">Address</label>
                <p className="text-gray-900">{selectedPharmacy.address}</p>
              </div>
              <div>
                <label className="label">Phone</label>
                <p className="text-gray-900">{selectedPharmacy.phone}</p>
              </div>
              <div>
                <label className="label">Email</label>
                <p className="text-gray-900">{selectedPharmacy.email}</p>
              </div>
              <div>
                <label className="label">License Number</label>
                <p className="text-gray-900">
                  {selectedPharmacy.licenseNumber}
                </p>
              </div>
              <div>
                <label className="label">Operating Hours</label>
                <p className="text-gray-900">
                  {selectedPharmacy.operatingHours}
                </p>
              </div>
              <div>
                <label className="label">Total Medicines</label>
                <p className="text-gray-900 font-medium">
                  {selectedPharmacy.totalMedicines}
                </p>
              </div>
              <div>
                <label className="label">Low Stock Items</label>
                <p className="text-gray-900">
                  {selectedPharmacy.lowStockItems}
                </p>
              </div>
              <div>
                <label className="label">Status</label>
                <div>{getStatusBadge(selectedPharmacy.status)}</div>
              </div>
            </div>
            <div className="pt-4 border-t border-gray-200">
              <button
                onClick={() => setShowViewModal(false)}
                className="btn-secondary w-full"
              >
                Close
              </button>
            </div>
          </div>
        )}
      </Modal>

      {/* Delete Confirmation Modal */}
      <ConfirmModal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        onConfirm={handleDeletePharmacy}
        title="Delete Pharmacy"
        message="Are you sure you want to delete this pharmacy? This action cannot be undone."
        confirmText="Delete"
        cancelText="Cancel"
        type="danger"
        isLoading={isLoading}
        item={selectedPharmacy?.name}
      />
    </div>
  );
});

Pharmacies.displayName = "Pharmacies";

export default Pharmacies;
